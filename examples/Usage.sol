// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "../src/Scan2EarnNFT.sol";
import "../src/NFTFactory.sol";
import "../src/Scan2EarnERC721.sol";
import "../src/Scan2EarnERC1155.sol";

/**
 * @title Usage Example
 * @dev Demonstrates how to use the Scan2Earn NFT system
 */
contract UsageExample {
    NFTFactory public factory;
    Scan2EarnNFT public nftManager;
    
    address public owner;
    
    constructor() {
        owner = msg.sender;
        
        // Deploy factory and NFT manager
        factory = new NFTFactory(owner);
        nftManager = new Scan2EarnNFT(owner);
    }
    
    /**
     * @dev Example 1: Create a gaming NFT collection
     */
    function createGamingCollection() external returns (uint256 collectionId) {
        require(msg.sender == owner, "Only owner");
        
        // 1. Create ERC721 contract for unique items
        address gameItemContract = factory.createERC721("Epic Game Items", "EGI");
        
        // 2. Prepare media URLs (weapons, armor, etc.)
        string[] memory mediaUrls = new string[](4);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmWeapon1";
        mediaUrls[1] = "https://ipfs.io/ipfs/QmArmor1";
        mediaUrls[2] = "https://ipfs.io/ipfs/QmShield1";
        mediaUrls[3] = "https://ipfs.io/ipfs/QmHelmet1";
        
        string[] memory mediaTypes = new string[](4);
        mediaTypes[0] = "image";
        mediaTypes[1] = "image";
        mediaTypes[2] = "image";
        mediaTypes[3] = "image";
        
        // 3. Prepare game attributes
        string[] memory attributeNames = new string[](6);
        attributeNames[0] = "rarity";
        attributeNames[1] = "attack";
        attributeNames[2] = "defense";
        attributeNames[3] = "durability";
        attributeNames[4] = "element";
        attributeNames[5] = "level_requirement";
        
        string[] memory attributeValues = new string[](6);
        attributeValues[0] = "legendary";
        attributeValues[1] = "150";
        attributeValues[2] = "120";
        attributeValues[3] = "100";
        attributeValues[4] = "fire";
        attributeValues[5] = "50";
        
        // 4. Create collection
        collectionId = nftManager.createCollection(
            gameItemContract,
            "Epic Game Items Collection",
            block.timestamp + 180 days, // 6 months
            5000, // Limited edition
            mediaUrls,
            mediaTypes,
            "Epic Game Items - Season 1",
            "Legendary weapons and armor for the ultimate gaming experience",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );
        
        return collectionId;
    }
    
    /**
     * @dev Example 2: Create a digital art collection
     */
    function createArtCollection() external returns (uint256 collectionId) {
        require(msg.sender == owner, "Only owner");
        
        // 1. Create ERC1155 contract for multiple editions
        address artContract = factory.createERC1155("https://api.artgallery.com/{id}.json");
        
        // 2. Prepare multimedia content
        string[] memory mediaUrls = new string[](5);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmArt1";      // Main artwork
        mediaUrls[1] = "https://ipfs.io/ipfs/QmArt1Video"; // Creation process video
        mediaUrls[2] = "https://ipfs.io/ipfs/QmArt1Audio"; // Artist commentary
        mediaUrls[3] = "https://ipfs.io/ipfs/QmArt1Sketch"; // Original sketch
        mediaUrls[4] = "https://ipfs.io/ipfs/QmArt1Certificate"; // Certificate of authenticity
        
        string[] memory mediaTypes = new string[](5);
        mediaTypes[0] = "image";
        mediaTypes[1] = "video";
        mediaTypes[2] = "audio";
        mediaTypes[3] = "image";
        mediaTypes[4] = "document";
        
        // 3. Prepare art attributes
        string[] memory attributeNames = new string[](8);
        attributeNames[0] = "artist";
        attributeNames[1] = "style";
        attributeNames[2] = "medium";
        attributeNames[3] = "year_created";
        attributeNames[4] = "dimensions";
        attributeNames[5] = "color_palette";
        attributeNames[6] = "inspiration";
        attributeNames[7] = "edition_size";
        
        string[] memory attributeValues = new string[](8);
        attributeValues[0] = "Digital Picasso";
        attributeValues[1] = "Neo-Digital";
        attributeValues[2] = "Digital Canvas";
        attributeValues[3] = "2024";
        attributeValues[4] = "4096x4096";
        attributeValues[5] = "Vibrant Blues";
        attributeValues[6] = "Ocean Waves";
        attributeValues[7] = "100";
        
        // 4. Create collection
        collectionId = nftManager.createCollection(
            artContract,
            "Digital Masters Collection",
            block.timestamp + 365 days, // 1 year
            10000, // Large collection
            mediaUrls,
            mediaTypes,
            "Digital Masters - Contemporary Art",
            "A curated collection of contemporary digital artworks by emerging artists",
            Scan2EarnNFT.NFTType.ERC1155,
            attributeNames,
            attributeValues
        );
        
        return collectionId;
    }
    
    /**
     * @dev Example 3: Create a utility/membership collection
     */
    function createMembershipCollection() external returns (uint256 collectionId) {
        require(msg.sender == owner, "Only owner");
        
        // 1. Create ERC721 for unique memberships
        address membershipContract = factory.createERC721("VIP Membership", "VIP");
        
        // 2. Prepare membership content
        string[] memory mediaUrls = new string[](3);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmVIPCard";
        mediaUrls[1] = "https://ipfs.io/ipfs/QmVIPVideo";
        mediaUrls[2] = "https://ipfs.io/ipfs/QmVIPBenefits";
        
        string[] memory mediaTypes = new string[](3);
        mediaTypes[0] = "image";
        mediaTypes[1] = "video";
        mediaTypes[2] = "document";
        
        // 3. Prepare membership attributes
        string[] memory attributeNames = new string[](7);
        attributeNames[0] = "tier";
        attributeNames[1] = "benefits";
        attributeNames[2] = "access_level";
        attributeNames[3] = "discount_percentage";
        attributeNames[4] = "exclusive_events";
        attributeNames[5] = "priority_support";
        attributeNames[6] = "transferable";
        
        string[] memory attributeValues = new string[](7);
        attributeValues[0] = "Platinum";
        attributeValues[1] = "All Premium Features";
        attributeValues[2] = "Level 5";
        attributeValues[3] = "25%";
        attributeValues[4] = "Yes";
        attributeValues[5] = "Yes";
        attributeValues[6] = "No";
        
        // 4. Create collection
        collectionId = nftManager.createCollection(
            membershipContract,
            "VIP Membership Collection",
            block.timestamp + 730 days, // 2 years
            1000, // Exclusive membership
            mediaUrls,
            mediaTypes,
            "VIP Membership - Platinum Tier",
            "Exclusive VIP membership with premium benefits and access",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );
        
        return collectionId;
    }
    
    /**
     * @dev Example 4: Query collection information
     */
    function getCollectionInfo(uint256 collectionId) external view returns (
        string memory name,
        string memory title,
        uint256 remaining,
        bool isExpired,
        uint256 mediaCount,
        uint256 attributeCount
    ) {
        // Get basic info
        (,name,,,, title,,,) = nftManager.getCollection(collectionId);
        
        // Get status info
        remaining = nftManager.getRemainingMintable(collectionId);
        isExpired = nftManager.isCollectionExpired(collectionId);
        
        // Get media and attributes count
        Scan2EarnNFT.MediaUrl[] memory urls = nftManager.getCollectionMediaUrls(collectionId);
        Scan2EarnNFT.Attribute[] memory attributes = nftManager.getCollectionAttributes(collectionId);
        
        mediaCount = urls.length;
        attributeCount = attributes.length;
    }
    
    /**
     * @dev Example 5: Batch mint for different scenarios
     */
    function batchMintExample(uint256 collectionId, address[] memory recipients) external {
        require(msg.sender == owner, "Only owner");
        require(recipients.length <= 10, "Too many recipients");
        
        // Mint one NFT to each recipient
        for (uint256 i = 0; i < recipients.length; i++) {
            nftManager.mintNFT(collectionId, recipients[i], i + 1, 1);
        }
    }
    
    /**
     * @dev Get factory statistics
     */
    function getFactoryStats() external view returns (
        uint256 totalERC721,
        uint256 totalERC1155,
        address[] memory myERC721s,
        address[] memory myERC1155s
    ) {
        totalERC721 = factory.getTotalERC721Contracts();
        totalERC1155 = factory.getTotalERC1155Contracts();
        myERC721s = factory.getCreatorERC721Contracts(owner);
        myERC1155s = factory.getCreatorERC1155Contracts(owner);
    }
}
