// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Script.sol";
import "../src/Scan2EarnNFT.sol";
import "../src/NFTFactory.sol";

contract DeploySimpleScript is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        vm.startBroadcast(deployerPrivateKey);
        
        console.log("Deploying contracts with deployer:", deployer);
        console.log("Deployer balance:", deployer.balance);
        
        // Deploy NFT Factory
        NFTFactory factory = new NFTFactory(deployer);
        console.log("NFTFactory deployed at:", address(factory));
        
        // Deploy NFT Manager
        Scan2EarnNFT nftManager = new Scan2EarnNFT(deployer);
        console.log("Scan2EarnNFT deployed at:", address(nftManager));
        
        vm.stopBroadcast();
        
        // Log deployment summary
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        console.log("NFTFactory:", address(factory));
        console.log("Scan2EarnNFT:", address(nftManager));
        console.log("========================");
    }
}
