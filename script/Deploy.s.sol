// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Script.sol";
import "../src/Scan2EarnNFTPublic.sol";
import "../src/NFTFactory.sol";

contract DeployScript is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);
        
        vm.startBroadcast(deployerPrivateKey);
        
        console.log("Deploying contracts with deployer:", deployer);
        console.log("Deployer balance:", deployer.balance);
        
        // Deploy NFT Factory
        NFTFactory factory = new NFTFactory(deployer);
        console.log("NFTFactory deployed at:", address(factory));
        
        // Deploy NFT Manager
        Scan2EarnNFTPublic nftManager = new Scan2EarnNFTPublic(deployer);
        console.log("Scan2EarnNFTPublic deployed at:", address(nftManager));
        

        
        vm.stopBroadcast();
        
        // Log deployment summary
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        console.log("NFTFactory:", address(factory));
        console.log("Scan2EarnNFTPublic:", address(nftManager));
        console.log("========================");
    }
}
