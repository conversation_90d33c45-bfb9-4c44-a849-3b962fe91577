// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Script.sol";
import "../src/Scan2EarnNFT.sol";
import "../src/NFTFactory.sol";

contract DeployScript is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("0x45c93a6719d1478805fd57ef2312b089b66b99a78b27f92f98f86e4d73ac4785");
        address deployer = vm.addr(deployerPrivateKey);
        
        vm.startBroadcast(deployerPrivateKey);
        
        console.log("Deploying contracts with deployer:", deployer);
        console.log("Deployer balance:", deployer.balance);
        
        // Deploy NFT Factory
        NFTFactory factory = new NFTFactory(deployer);
        console.log("NFTFactory deployed at:", address(factory));
        
        // Deploy NFT Manager
        Scan2EarnNFT nftManager = new Scan2EarnNFT(deployer);
        console.log("Scan2EarnNFT deployed at:", address(nftManager));
        
        // Example: Create sample ERC721 and ERC1155 contracts
        address erc721Address = factory.createERC721("Scan2Earn NFT", "S2E");
        console.log("Sample ERC721 deployed at:", erc721Address);
        
        address erc1155Address = factory.createERC1155("https://api.scan2earn.com/metadata/{id}.json");
        console.log("Sample ERC1155 deployed at:", erc1155Address);
        
        // Example: Create a sample collection
        string[] memory mediaUrls = new string[](2);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmSampleHash1";
        mediaUrls[1] = "https://ipfs.io/ipfs/QmSampleHash2";
        
        string[] memory mediaTypes = new string[](2);
        mediaTypes[0] = "image";
        mediaTypes[1] = "video";
        
        string[] memory attributeNames = new string[](3);
        attributeNames[0] = "rarity";
        attributeNames[1] = "power";
        attributeNames[2] = "element";
        
        string[] memory attributeValues = new string[](3);
        attributeValues[0] = "legendary";
        attributeValues[1] = "100";
        attributeValues[2] = "fire";
        
        uint256 collectionId = nftManager.createCollection(
            erc721Address,
            "Genesis Collection",
            block.timestamp + 365 days, // 1 year expiration
            10000, // Max 10,000 NFTs
            mediaUrls,
            mediaTypes,
            "Scan2Earn Genesis NFT Collection",
            "The first official collection of Scan2Earn NFTs with unique scanning rewards",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );
        
        console.log("Sample collection created with ID:", collectionId);
        
        vm.stopBroadcast();
        
        // Log deployment summary
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        console.log("NFTFactory:", address(factory));
        console.log("Scan2EarnNFT:", address(nftManager));
        console.log("Sample ERC721:", erc721Address);
        console.log("Sample ERC1155:", erc1155Address);
        console.log("Sample Collection ID:", collectionId);
        console.log("========================");
    }
}
