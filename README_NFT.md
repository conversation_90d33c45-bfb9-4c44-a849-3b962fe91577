# Scan2Earn NFT Contract System

Một hệ thống NFT contract hoàn chỉnh hỗ trợ cả ERC721 và ERC1155 với các tính năng quản lý collection nâng cao, được xây dựng với Solidity 0.8.26 và OpenZeppelin v5.

## ✨ Tính năng chính

### 🎯 Quản lý Collection NFT
- **Address NFT Contract**: Địa chỉ contract NFT (ERC721 hoặc ERC1155)
- **Tên Collection**: Tên của bộ sưu tập NFT
- **Hạn sử dụng**: Thời gian hết hạn của collection
- **Số lượng mint tối đa**: Giới hạn số NFT có thể mint
- **Media URLs**: Lưu trữ tối đa 10 URL cho ảnh/video/audio với loại media
- **Metadata**: <PERSON>i<PERSON><PERSON> đề, mô tả chi tiết
- **Loại NFT**: Hỗ trợ cả ERC721 và ERC1155
- **Attributes**: <PERSON><PERSON><PERSON> trữ các cặp name:value tùy chỉnh (không giới hạn số lượng)

### 🏭 NFT Factory
- Tạo contract ERC721 và ERC1155 một cách dễ dàng
- Theo dõi tất cả contract đã deploy
- Quản lý ownership cho từng creator

### 🔧 Tính năng nâng cao
- **ReentrancyGuard**: Bảo vệ khỏi tấn công reentrancy
- **Ownable**: Quản lý quyền truy cập với OpenZeppelin v5
- **Batch Operations**: Mint nhiều NFT cùng lúc
- **Metadata Management**: Cập nhật metadata linh hoạt
- **Supply Management**: Quản lý supply cho ERC1155

## 📁 Cấu trúc Contract

```
src/
├── Scan2EarnNFT.sol      # Contract chính quản lý collections
├── Scan2EarnERC721.sol   # Implementation ERC721 với metadata
├── Scan2EarnERC1155.sol  # Implementation ERC1155 với supply control
└── NFTFactory.sol        # Factory để tạo NFT contracts

test/
└── Scan2EarnNFT.t.sol    # Comprehensive test suite

script/
└── Deploy.s.sol          # Deployment script
```

## 🚀 Cài đặt và Sử dụng

### Yêu cầu
- [Foundry](https://book.getfoundry.sh/getting-started/installation)
- Solidity ^0.8.26
- OpenZeppelin Contracts v5

### Cài đặt dependencies

```bash
# Clone repository
git clone <your-repo>
cd scan2earn-contract

# Cài đặt OpenZeppelin contracts
forge install OpenZeppelin/openzeppelin-contracts

# Build contracts
forge build
```

### Chạy tests

```bash
# Chạy tất cả tests
forge test

# Chạy tests với gas report
forge test --gas-report

# Chạy tests với coverage
forge coverage
```

### Deploy

```bash
# Tạo file .env với PRIVATE_KEY
echo "PRIVATE_KEY=your_private_key_here" > .env

# Deploy lên testnet/mainnet
forge script script/Deploy.s.sol:DeployScript --rpc-url <your_rpc_url> --broadcast --verify
```

## 💻 Sử dụng Contract

### 1. Tạo NFT Contract qua Factory

```solidity
// Deploy factory
NFTFactory factory = new NFTFactory(owner);

// Tạo ERC721 contract
address erc721Address = factory.createERC721("My NFT Collection", "MNC");

// Tạo ERC1155 contract  
address erc1155Address = factory.createERC1155("https://api.example.com/{id}.json");
```

### 2. Tạo Collection với đầy đủ metadata

```solidity
// Chuẩn bị media URLs (tối đa 10)
string[] memory mediaUrls = new string[](3);
mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";
mediaUrls[1] = "https://ipfs.io/ipfs/QmHash2";
mediaUrls[2] = "https://ipfs.io/ipfs/QmHash3";

string[] memory mediaTypes = new string[](3);
mediaTypes[0] = "image";
mediaTypes[1] = "video";
mediaTypes[2] = "audio";

// Chuẩn bị attributes (không giới hạn số lượng)
string[] memory attributeNames = new string[](5);
attributeNames[0] = "rarity";
attributeNames[1] = "power";
attributeNames[2] = "element";
attributeNames[3] = "generation";
attributeNames[4] = "creator";

string[] memory attributeValues = new string[](5);
attributeValues[0] = "legendary";
attributeValues[1] = "100";
attributeValues[2] = "fire";
attributeValues[3] = "1";
attributeValues[4] = "Scan2Earn Team";

// Tạo collection
uint256 collectionId = nftManager.createCollection(
    erc721Address,
    "Genesis Collection",
    block.timestamp + 365 days, // Hết hạn sau 1 năm
    10000, // Tối đa 10,000 NFTs
    mediaUrls,
    mediaTypes,
    "Scan2Earn Genesis NFT Collection",
    "The first official collection of Scan2Earn NFTs with unique scanning rewards",
    Scan2EarnNFT.NFTType.ERC721,
    attributeNames,
    attributeValues
);
```

### 3. Mint NFT

```solidity
// Mint single NFT
nftManager.mintNFT(collectionId, userAddress, tokenId, 1);

// Cho ERC1155, có thể mint nhiều copies
nftManager.mintNFT(collectionId, userAddress, tokenId, 100);
```

### 4. Truy vấn thông tin Collection

```solidity
// Lấy thông tin cơ bản
(
    address nftContract,
    string memory name,
    uint256 expirationTime,
    uint256 maxMintable,
    uint256 currentMinted,
    string memory title,
    string memory description,
    Scan2EarnNFT.NFTType nftType,
    bool isActive
) = nftManager.getCollection(collectionId);

// Lấy media URLs
Scan2EarnNFT.MediaUrl[] memory urls = nftManager.getCollectionMediaUrls(collectionId);

// Lấy attributes
Scan2EarnNFT.Attribute[] memory attributes = nftManager.getCollectionAttributes(collectionId);

// Kiểm tra trạng thái
bool expired = nftManager.isCollectionExpired(collectionId);
uint256 remaining = nftManager.getRemainingMintable(collectionId);
```

## 🏗️ Kiến trúc Data Structures

### MediaUrl Struct
```solidity
struct MediaUrl {
    string url;        // IPFS hash hoặc HTTP URL
    string mediaType;  // "image", "video", "audio", "document", etc.
}
```

### Attribute Struct
```solidity
struct Attribute {
    string name;   // Tên thuộc tính (vd: "rarity", "power")
    string value;  // Giá trị thuộc tính (vd: "legendary", "100")
}
```

### NFTCollection Struct
```solidity
struct NFTCollection {
    address nftContract;           // Địa chỉ contract NFT
    string name;                   // Tên collection
    uint256 expirationTime;        // Timestamp hết hạn
    uint256 maxMintable;           // Số lượng mint tối đa
    uint256 currentMinted;         // Số lượng đã mint
    MediaUrl[10] mediaUrls;        // Array cố định 10 URLs
    uint8 mediaUrlCount;           // Số lượng URL thực tế sử dụng
    string title;                  // Tiêu đề collection
    string description;            // Mô tả chi tiết
    NFTType nftType;              // ERC721 hoặc ERC1155
    Attribute[] attributes;        // Dynamic array attributes
    bool isActive;                // Trạng thái hoạt động
}
```

## ⚡ Gas Optimization

- **Solidity 0.8.26**: Sử dụng phiên bản mới nhất với optimizer
- **Packed Structs**: Tối ưu storage layout
- **Batch Operations**: Giảm gas cost cho multiple operations
- **Efficient Arrays**: Fixed array cho media URLs, dynamic cho attributes
- **Supply Tracking**: Optimized cho ERC1155

## 🔒 Security Features

- ✅ **ReentrancyGuard**: Bảo vệ khỏi reentrancy attacks
- ✅ **Ownable v5**: Access control với OpenZeppelin v5
- ✅ **Input Validation**: Kiểm tra tất cả inputs
- ✅ **Safe Math**: Built-in overflow protection
- ✅ **Proper Error Handling**: Custom errors và require statements
- ✅ **Supply Management**: Kiểm soát supply cho ERC1155

## 🧪 Testing

Test suite bao gồm:
- ✅ Collection creation cho cả ERC721 và ERC1155
- ✅ Media URLs management
- ✅ Attributes management  
- ✅ Factory deployment tracking
- ✅ Access control testing
- ✅ Edge cases và error conditions

```bash
# Chạy tests với verbose output
forge test -vvv

# Test specific function
forge test --match-test testCreateERC721Collection
```

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch  
5. Tạo Pull Request

## 📞 Support

Nếu có vấn đề hoặc câu hỏi, vui lòng tạo issue trên GitHub repository.
