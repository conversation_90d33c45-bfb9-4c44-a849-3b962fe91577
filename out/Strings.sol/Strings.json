{"abi": [{"type": "error", "name": "StringsInsufficientHexLength", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "StringsInvalidAddressFormat", "inputs": []}, {"type": "error", "name": "StringsInvalidChar", "inputs": []}], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea2646970667358221220ea97a9594be7130104b350d2a030cc4666d1546b9a5150e64c43d64741cc1c7564736f6c634300081a0033", "sourceMap": "297:18982:44:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea2646970667358221220ea97a9594be7130104b350d2a030cc4666d1546b9a5150e64c43d64741cc1c7564736f6c634300081a0033", "sourceMap": "297:18982:44:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"StringsInsufficientHexLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StringsInvalidAddressFormat\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StringsInvalidChar\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"String operations.\",\"errors\":{\"StringsInsufficientHexLength(uint256,uint256)\":[{\"details\":\"The `value` string doesn't fit in the specified `length`.\"}],\"StringsInvalidAddressFormat()\":[{\"details\":\"The string being parsed is not a properly formatted address.\"}],\"StringsInvalidChar()\":[{\"details\":\"The string being parsed contains characters that are not in scope of the given base.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":\"Strings\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":1000},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"******************************************aaad8a756315c532bf79f9f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://15e3599867c2182f5831e9268b274b2ef2047825837df6b4d81c9e89254b093e\",\"dweb:/ipfs/QmZbL7XAYr5RmaNaooPgZRmcDXaudfsYQfYD9y5iAECvpS\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "StringsInsufficientHexLength"}, {"inputs": [], "type": "error", "name": "StringsInvalidAddressFormat"}, {"inputs": [], "type": "error", "name": "StringsInvalidChar"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 1000}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts/contracts/utils/Strings.sol": "Strings"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "******************************************aaad8a756315c532bf79f9f4", "urls": ["bzz-raw://15e3599867c2182f5831e9268b274b2ef2047825837df6b4d81c9e89254b093e", "dweb:/ipfs/QmZbL7XAYr5RmaNaooPgZRmcDXaudfsYQfYD9y5iAECvpS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}}, "version": 1}, "id": 44}