{"id": "77c19c3de2ab5a12", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/StdAssertions.sol", "2": "lib/forge-std/src/StdChains.sol", "3": "lib/forge-std/src/StdCheats.sol", "4": "lib/forge-std/src/StdConstants.sol", "5": "lib/forge-std/src/StdError.sol", "6": "lib/forge-std/src/StdInvariant.sol", "7": "lib/forge-std/src/StdJson.sol", "8": "lib/forge-std/src/StdMath.sol", "9": "lib/forge-std/src/StdStorage.sol", "10": "lib/forge-std/src/StdStyle.sol", "11": "lib/forge-std/src/StdToml.sol", "12": "lib/forge-std/src/StdUtils.sol", "13": "lib/forge-std/src/Test.sol", "14": "lib/forge-std/src/Vm.sol", "15": "lib/forge-std/src/console.sol", "16": "lib/forge-std/src/console2.sol", "17": "lib/forge-std/src/interfaces/IMulticall3.sol", "18": "lib/forge-std/src/safeconsole.sol", "19": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "20": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "21": "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "22": "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "23": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "24": "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "25": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "26": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "27": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "28": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "32": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "33": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "34": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "35": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "36": "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "37": "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "38": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "39": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "40": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "41": "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "42": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "43": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "44": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "45": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "46": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "47": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "48": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "49": "src/NFTFactory.sol", "50": "src/Scan2EarnERC1155.sol", "51": "src/Scan2EarnERC721.sol", "52": "src/Scan2EarnNFT.sol", "53": "test/Scan2EarnNFT.t.sol"}, "language": "Solidity"}