{"id": "09e1a6ccb0cce5d5", "source_id_to_path": {"0": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "1": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "2": "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "3": "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "4": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "5": "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "6": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "7": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "8": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "9": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "10": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "11": "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "12": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "13": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "14": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "15": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "16": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "17": "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "18": "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "19": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "20": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "21": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "22": "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "23": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "24": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "25": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "26": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "27": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "28": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "29": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "30": "src/Scan2EarnERC1155.sol", "31": "src/Scan2EarnERC721.sol", "32": "src/Scan2EarnNFTFinal.sol"}, "language": "Solidity"}