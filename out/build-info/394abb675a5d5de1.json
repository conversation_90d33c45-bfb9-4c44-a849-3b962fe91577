{"id": "394abb675a5d5de1", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/Script.sol", "2": "lib/forge-std/src/StdAssertions.sol", "3": "lib/forge-std/src/StdChains.sol", "4": "lib/forge-std/src/StdCheats.sol", "5": "lib/forge-std/src/StdConstants.sol", "6": "lib/forge-std/src/StdError.sol", "7": "lib/forge-std/src/StdInvariant.sol", "8": "lib/forge-std/src/StdJson.sol", "9": "lib/forge-std/src/StdMath.sol", "10": "lib/forge-std/src/StdStorage.sol", "11": "lib/forge-std/src/StdStyle.sol", "12": "lib/forge-std/src/StdToml.sol", "13": "lib/forge-std/src/StdUtils.sol", "14": "lib/forge-std/src/Test.sol", "15": "lib/forge-std/src/Vm.sol", "16": "lib/forge-std/src/console.sol", "17": "lib/forge-std/src/console2.sol", "18": "lib/forge-std/src/interfaces/IMulticall3.sol", "19": "lib/forge-std/src/safeconsole.sol", "20": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "21": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "22": "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "23": "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "24": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "25": "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "26": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "27": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "28": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "32": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "33": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "34": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "35": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "36": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "37": "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "38": "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "39": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "40": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "41": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "42": "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "43": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "44": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "45": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "46": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "47": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "48": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "49": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "50": "script/Deploy.s.sol", "51": "script/DeploySimple.s.sol", "52": "src/NFTFactory.sol", "53": "src/Scan2EarnERC1155.sol", "54": "src/Scan2EarnERC721.sol", "55": "src/Scan2EarnNFT.sol", "56": "test/Scan2EarnNFT.t.sol"}, "language": "Solidity"}