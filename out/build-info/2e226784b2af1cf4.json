{"id": "2e226784b2af1cf4", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/Script.sol", "2": "lib/forge-std/src/StdChains.sol", "3": "lib/forge-std/src/StdCheats.sol", "4": "lib/forge-std/src/StdConstants.sol", "5": "lib/forge-std/src/StdJson.sol", "6": "lib/forge-std/src/StdMath.sol", "7": "lib/forge-std/src/StdStorage.sol", "8": "lib/forge-std/src/StdStyle.sol", "9": "lib/forge-std/src/StdUtils.sol", "10": "lib/forge-std/src/Vm.sol", "11": "lib/forge-std/src/console.sol", "12": "lib/forge-std/src/console2.sol", "13": "lib/forge-std/src/interfaces/IMulticall3.sol", "14": "lib/forge-std/src/safeconsole.sol", "15": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "16": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "17": "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "18": "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "19": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "20": "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "21": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "22": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "23": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "24": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "25": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "26": "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "27": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "28": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "32": "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "33": "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "34": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "35": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "36": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "37": "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "38": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "39": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "40": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "41": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "42": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "43": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "44": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "45": "script/Deploy.s.sol", "46": "src/NFTFactory.sol", "47": "src/Scan2EarnERC1155.sol", "48": "src/Scan2EarnERC721.sol", "49": "src/Scan2EarnNFTPublic.sol"}, "language": "Solidity"}