{"abi": [{"type": "function", "name": "arithmeticError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "assertionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "divisionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "encodeStorageError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "enumConversionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "indexOOBError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "memOverflowError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "popError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "zeroVarError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}], "bytecode": {"object": "0x608080604052346019576102f7908161001e823930815050f35b5f80fdfe6080806040526004361015610012575f80fd5b5f3560e01c90816305ee8612146102335750806310332977146101ff5780631de45560146101cb5780638995290f14610197578063986c5f6814610163578063b22dc54d1461012f578063b67689da146100fb578063d160e4de146100c75763fa784a441461007f575f80fd5b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260126024820152602481526100b3604482610261565b60405191829182610297565b0390f35b5f80fd5b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260226024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260516024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260316024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260416024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260116024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260216024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260016024820152602481526100b3604482610261565b5f3660031901126100c3576100bf90634e487b7160e01b602082015260326024820152602481526100b36044825b90601f8019910116810190811067ffffffffffffffff82111761028357604052565b634e487b7160e01b5f52604160045260245ffd5b602060409281835280519182918282860152018484015e5f828201840152601f01601f191601019056fea26469706673582212206450c238d278e6d35735b0fef60cb3cf765ed2cf69ac61e638ec50dd9a66440164736f6c634300081a0033", "sourceMap": "162:850:6:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080806040526004361015610012575f80fd5b5f3560e01c90816305ee8612146102335750806310332977146101ff5780631de45560146101cb5780638995290f14610197578063986c5f6814610163578063b22dc54d1461012f578063b67689da146100fb578063d160e4de146100c75763fa784a441461007f575f80fd5b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260126024820152602481526100b3604482610261565b60405191829182610297565b0390f35b5f80fd5b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260226024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260516024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260316024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260416024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260116024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260216024820152602481526100b3604482610261565b5f3660031901126100c3576100bf604051634e487b7160e01b602082015260016024820152602481526100b3604482610261565b5f3660031901126100c3576100bf90634e487b7160e01b602082015260326024820152602481526100b36044825b90601f8019910116810190811067ffffffffffffffff82111761028357604052565b634e487b7160e01b5f52604160045260245ffd5b602060409281835280519182918282860152018484015e5f828201840152601f01601f191601019056fea26469706673582212206450c238d278e6d35735b0fef60cb3cf765ed2cf69ac61e638ec50dd9a66440164736f6c634300081a0033", "sourceMap": "162:850:6:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;408;;;;450:4;408:47;;;162:850;408:47;;;;;;;:::i;:::-;162:850;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;601;;;;643:4;601:47;;;162:850;601:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;962;;;;1004:4;962:47;;;162:850;962:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;687;;;;729:4;687:47;;;162:850;687:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;872;;;;914:4;872:47;;;162:850;872:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;317;;;;359:4;317:47;;;162:850;317:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;505;;;;547:4;505:47;;;162:850;505:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;;;778:47;;;224;;;;266:4;224:47;;;162:850;224:47;;;;;;;:::i;162:850::-;;;-1:-1:-1;;162:850:6;;;;;778:47;;;;;;;;820:4;778:47;;;162:850;778:47;;;;;;162:850;;;;;;;;;;;;;;;;;;;;;:::o;:::-;778:47;;;-1:-1:-1;162:850:6;;;;;-1:-1:-1;162:850:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;162:850:6;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"arithmeticError()": "8995290f", "assertionError()": "10332977", "divisionError()": "fa784a44", "encodeStorageError()": "d160e4de", "enumConversionError()": "1de45560", "indexOOBError()": "05ee8612", "memOverflowError()": "986c5f68", "popError()": "b22dc54d", "zeroVarError()": "b67689da"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"arithmeticError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assertionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"divisionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"encodeStorageError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enumConversionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"indexOOBError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"memOverflowError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"popError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"zeroVarError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdError.sol\":\"stdError\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "arithmeticError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "assertionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "divisionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "encodeStorageError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "enumConversionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "indexOOBError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "memOverflowError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "popError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "zeroVarError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdError.sol": "stdError"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}}, "version": 1}, "id": 6}