{"abi": [{"type": "constructor", "inputs": [{"name": "baseURI", "type": "string", "internalType": "string"}, {"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfBatch", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}, {"name": "ids", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "createNewToken", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "tokenURI", "type": "string", "internalType": "string"}, {"name": "metadata", "type": "string", "internalType": "string"}, {"name": "maxSupply", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exists", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentTokenId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxSupply", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRemainingSupply", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTokenMetadata", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "tokenURI", "type": "string", "internalType": "string"}, {"name": "metadata", "type": "string", "internalType": "string"}, {"name": "maxSupply", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintBatch", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "ids", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "tokenURIs", "type": "string[]", "internalType": "string[]"}, {"name": "metadatas", "type": "string[]", "internalType": "string[]"}, {"name": "maxSupplies", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeBatchTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "ids", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateTokenMetadata", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "metadata", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateTokenURI", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "newURI", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "uri", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TransferBatch", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "ids", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}, {"name": "values", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "TransferSingle", "inputs": [{"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "id", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "URI", "inputs": [{"name": "value", "type": "string", "indexed": false, "internalType": "string"}, {"name": "id", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC1155InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC1155InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1155InvalidArrayLength", "inputs": [{"name": "idsLength", "type": "uint256", "internalType": "uint256"}, {"name": "valuesLength", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC1155InvalidOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1155InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1155InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1155MissingApprovalForAll", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "249:4689:52:-:0;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;-1:-1:-1;;249:4689:52;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;10164:13:25;249:4689:52;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;10164:13:25;249:4689:52;;1273:26:20;;1269:95;;3004:6;249:4689:52;;-1:-1:-1;;;;;;249:4689:52;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;3052:40:20;-1:-1:-1;;3052:40:20;249:4689:52;783:16;249:4689;;;;;;;;1269:95:20;1322:31;;;-1:-1:-1;1322:31:20;-1:-1:-1;1322:31:20;249:4689:52;;-1:-1:-1;1322:31:20;249:4689:52;;;;-1:-1:-1;249:4689:52;;;;;;;;;;10164:13:25;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;10164:13:25;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10164:13:25;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;-1:-1:-1;249:4689:52;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;;-1:-1:-1;;249:4689:52;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::o", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "249:4689:52:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;249:4689:52;;;;;;:::o;:::-;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;:::o;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;:::o;:::-;;;;;;-1:-1:-1;;249:4689:52;;;;;2290:22:25;249:4689:52;;:::i;:::-;;;-1:-1:-1;249:4689:52;-1:-1:-1;249:4689:52;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;2290:22:25;249:4689:52;;;;;;;;-1:-1:-1;;;;;;249:4689:52;;;;;:::o;:::-;;;;;;-1:-1:-1;;249:4689:52;;;;;;;;;;:::i;:::-;;;;;1497:26:25;;;1482:41;;:109;;;;;249:4689:52;1482:161:25;;;;249:4689:52;;;;;;;;;;1482:161:25;-1:-1:-1;;;829:40:45;;-1:-1:-1;1482:161:25;;;:109;-1:-1:-1;;;1539:52:25;;;-1:-1:-1;1482:109:25;;249:4689:52;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;-1:-1:-1;;249:4689:52;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;:::o;:::-;;;;;;-1:-1:-1;;249:4689:52;;;;;;-1:-1:-1;249:4689:52;3223:10;249:4689;;;;-1:-1:-1;249:4689:52;;:::i;:::-;;;3259:26;:58;;249:4689;3259:58;;249:4689;;;;;;;:::i;:::-;;;;3259:58;249:4689;;;-1:-1:-1;2141:4:25;249:4689:52;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3259:58;;249:4689;2141:4:25;-1:-1:-1;249:4689:52;;;;;;-1:-1:-1;;249:4689:52;;;;;;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;:::o;:::-;;:::i;:::-;-1:-1:-1;;;;;249:4689:52;;;;;;-1:-1:-1;;249:4689:52;;;;:::o;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;249:4689:52;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;1500:62:20;;;:::i;:::-;3824:48:52;3832:15;;-1:-1:-1;249:4689:52;4449:10;249:4689;;;-1:-1:-1;249:4689:52;;4449:23;;4363:116;;3832:15;3824:48;:::i;:::-;-1:-1:-1;249:4689:52;3882:10;249:4689;;;-1:-1:-1;249:4689:52;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1500:62:20;;;:::i;:::-;3625:48:52;3633:15;;-1:-1:-1;249:4689:52;4449:10;249:4689;;;-1:-1:-1;249:4689:52;;4449:23;;4363:116;;3625:48;-1:-1:-1;249:4689:52;3683:14;249:4689;;;-1:-1:-1;249:4689:52;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;:::i;:::-;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;249:4689:52;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;;;-1:-1:-1;249:4689:52;4449:10;249:4689;;;-1:-1:-1;249:4689:52;;4449:23;;4363:116;;249:4689;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;4334:12;249:4689;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;-1:-1:-1;249:4689:52;4013:10;249:4689;;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;3423:48;3431:15;;-1:-1:-1;249:4689:52;4449:10;249:4689;;;-1:-1:-1;249:4689:52;;4449:23;;4363:116;;3423:48;-1:-1:-1;249:4689:52;3488:14;249:4689;;;;;-1:-1:-1;249:4689:52;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;249:4689:52;;;;;1478:1121;249:4689;;;;;;:::i;:::-;1478:1121;;:::i;249:4689::-;;;;;;-1:-1:-1;;249:4689:52;;;;1500:62:20;;:::i;:::-;3004:6;249:4689:52;;-1:-1:-1;;;;;;249:4689:52;;;;;;;-1:-1:-1;;;;;249:4689:52;3052:40:20;249:4689:52;;3052:40:20;249:4689:52;;;;;;;-1:-1:-1;;249:4689:52;;;;;;4134:48;4142:15;;-1:-1:-1;249:4689:52;4449:10;249:4689;;;-1:-1:-1;249:4689:52;;4449:23;;4363:116;;4134:48;249:4689;-1:-1:-1;249:4689:52;4199:10;249:4689;;;-1:-1:-1;249:4689:52;;;-1:-1:-1;249:4689:52;1131:12:28;249:4689:52;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;249:4689:52;;;;1710:6:20;249:4689:52;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;13191:22:25;;13187:94;;735:10:39;-1:-1:-1;249:4689:52;;;13290:18:25;249:4689:52;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;13351:41:25;249:4689:52;;735:10:39;13351:41:25;735:10:39;13351:41:25;;249:4689:52;;;;;;;;;;;;13351:41:25;;;;249:4689:52;13187:94:25;13236:34;;;-1:-1:-1;13236:34:25;-1:-1:-1;249:4689:52;;;-1:-1:-1;13236:34:25;249:4689:52;;;;;;-1:-1:-1;;249:4689:52;;;;;;;;-1:-1:-1;249:4689:52;1131:12:28;249:4689:52;;;-1:-1:-1;249:4689:52;;1043:111:28;;249:4689:52;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;2827:7;249:4689;;;;;;:::i;:::-;2827:7;;:::i;:::-;249:4689;;;;;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;816:652;249:4689;;;;;;:::i;:::-;816:652;;:::i;249:4689::-;;;;;;-1:-1:-1;;249:4689:52;;;;;;3355:37:25;249:4689:52;;:::i;:::-;;;:::i;:::-;-1:-1:-1;;;;;249:4689:52;;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;3355:37:25;249:4689:52;;;;;;;;;;;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;:::i;:::-;;-1:-1:-1;;;;;249:4689:52;;735:10:39;3600:14:25;;;;:49;;249:4689:52;3596:129:25;;-1:-1:-1;;;;;249:4689:52;;8089:16:25;8085:88;;8186:18;8182:88;;8418:4;8329:29;;13683:648;;;;;;;;;;;;;;;;;;;;;;;;;;13515:822;8329:29;8418:4;;;;:::i;8182:88::-;8227:32;;;-1:-1:-1;8227:32:25;-1:-1:-1;249:4689:52;;;-1:-1:-1;8227:32:25;8085:88;8128:34;;;-1:-1:-1;8128:34:25;-1:-1:-1;249:4689:52;;;-1:-1:-1;8128:34:25;3596:129;3672:42;;;-1:-1:-1;3672:42:25;735:10:39;249:4689:52;;;;;-1:-1:-1;3672:42:25;3600:49;-1:-1:-1;;249:4689:52;;;3355:18:25;249:4689:52;;;;;;;;735:10:39;249:4689:52;;;;;;;;;;3618:31:25;3600:49;;249:4689:52;;;;;;-1:-1:-1;;249:4689:52;;;;;;:::i;:::-;1500:62:20;;:::i;:::-;-1:-1:-1;;;;;249:4689:52;2627:22:20;;2623:91;;3004:6;249:4689:52;;-1:-1:-1;;;;;;249:4689:52;;;;;;;-1:-1:-1;;;;;249:4689:52;3052:40:20;-1:-1:-1;;3052:40:20;249:4689:52;2623:91:20;2672:31;;;-1:-1:-1;2672:31:20;-1:-1:-1;249:4689:52;;;-1:-1:-1;2672:31:20;249:4689:52;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;;;-1:-1:-1;249:4689:52;;;;;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;249:4689:52;;-1:-1:-1;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;249:4689:52;;;;;;;;;-1:-1:-1;249:4689:52;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3820:429:25;;;;;-1:-1:-1;;;;;249:4689:52;;735:10:39;4057:14:25;;;;:49;;3820:429;4053:129;;-1:-1:-1;;;;;249:4689:52;;9024:16:25;9020:88;;9121:18;9117:88;;9264:4;;;:::i;4057:49::-;-1:-1:-1;;249:4689:52;;;3355:18:25;249:4689:52;;;;;;;;735:10:39;249:4689:52;;;;;;;;;;4075:31:25;4057:49;;249:4689:52;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;2476:552:25;;;;249:4689:52;;;;2632:29:25;;;2628:121;;249:4689:52;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;2874:3:25;249:4689:52;;2853:19:25;;;;;17194:82:37;2912:68:25;249:4689:52;17194:82:37;;;249:4689:52;17194:82:37;;;;;;17992;;;;;2912:68:25;2290:9;249:4689:52;;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;2187:132:25;;2912:68;2893:87;;;;:::i;:::-;249:4689:52;;2838:13:25;;2853:19;-1:-1:-1;2853:19:25;;-1:-1:-1;;2476:552:25:o;2628:121::-;2684:54;;;-1:-1:-1;2684:54:25;;249:4689:52;;;;-1:-1:-1;2684:54:25;1500:62:20;;;;;;;;;;;;:::i;:::-;249:4689:52;;;;1770:28;;;:79;;;;1500:62:20;1770:132:52;;;;1500:62:20;1770:186:52;;;1500:62:20;1749:255:52;;;:::i;:::-;-1:-1:-1;2059:3:52;249:4689;;2043:14;;;;;2086:6;2078:54;2086:6;249:4689;2086:6;;;:::i;:::-;249:4689;2086:10;;2078:54;:::i;:::-;2163:18;2174:6;;;;:::i;:::-;249:4689;;;2163:10;249:4689;;;;;;;2163:18;249:4689;2163:23;2159:255;;2059:3;2440:89;2448:32;:19;2460:6;;;;:::i;:::-;249:4689;-1:-1:-1;249:4689:52;1131:12:28;249:4689:52;;;-1:-1:-1;249:4689:52;;1043:111:28;;2448:19:52;2470:10;;;;:::i;:::-;249:4689;2448:32;;:::i;:::-;2484:18;2495:6;;;;:::i;2484:18::-;249:4689;-1:-1:-1;2448:54:52;2440:89;:::i;:::-;249:4689;2028:13;;2159:255;2279:14;;;;:::i;:::-;249:4689;2258:18;2269:6;;;;:::i;2258:18::-;249:4689;;2332:12;;;;:::i;:::-;;2311:18;2322:6;;;;:::i;:::-;249:4689;;;2311:10;249:4689;;;;;;;2311:18;249:4689;:::i;:::-;;2387:12;;;;:::i;:::-;;2362:22;2377:6;;;;:::i;:::-;249:4689;;;2362:14;249:4689;;;;;;;;2159:255;;2043:14;;;;;;;;2587:4;2043:14;2587:4;:::i;1770:186::-;-1:-1:-1;249:4689:52;;;;1918:38;1770:186;;:132;249:4689;;;;;1866:36;1770:132;;:79;249:4689;;;;1815:34;1770:79;;;249:4689;;;;:::o;:::-;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;:::o;:::-;;;-1:-1:-1;;;249:4689:52;;;;;;;;;;;;-1:-1:-1;;;249:4689:52;;;;;;;1500:62:20;;;;;;;;:::i;:::-;2864:14:52;249:4689;;-1:-1:-1;;249:4689:52;;;;;;;2864:14;249:4689;;;;2897:10;249:4689;;;;;;;;;2938:10;249:4689;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;3058:4;249:4689;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2978:23;;249:4689;;2362:14;249:4689;;;;;;;;3058:4;:::i;249:4689::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3058:4;249:4689;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1500:62:20;;;;;;;;;;:::i;:::-;1045:50:52;1053:6;;;1045:50;:::i;:::-;249:4689;1058:1;249:4689;1118:10;249:4689;;;1058:1;249:4689;;1118:19;1114:206;;1500:62:20;1131:16:28;;1456:4:52;1131:16:28;;;1338:77:52;1346:24;1131:16:28;;;249:4689:52;;1131:12:28;249:4689:52;;;;;;;1131:16:28;249:4689:52;1346:24;:::i;:::-;1374:14;;249:4689;;2163:10;249:4689;;;;;;;1114:206;249:4689;1058:1;249:4689;1118:10;249:4689;;;1058:1;249:4689;;;1058:1;249:4689;1241:10;249:4689;;;1058:1;249:4689;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;1456:4;249:4689;;1058:1;249:4689;;;;;;;;;;;;;;;;;;;;;1114:206;;;;;249:4689;;;;;;;;;;;;;;;;;1058:1;249:4689;;;;;;;;;;;;;;;;1456:4;249:4689;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1796:162:20;1710:6;249:4689:52;-1:-1:-1;;;;;249:4689:52;735:10:39;1855:23:20;1851:101;;1796:162::o;1851:101::-;1901:40;;;-1:-1:-1;1901:40:20;735:10:39;1901:40:20;249:4689:52;;-1:-1:-1;1901:40:20;11339:282:25;;;;-1:-1:-1;;;;;249:4689:52;;11460:16:25;11456:88;;11609:4;;;:::i;10562:346::-;;;;;-1:-1:-1;;;;;249:4689:52;;10658:16:25;10654:88;;10896:4;10801:29;;13683:648;;;;;;;;;;;;;;;;;;;;;;;;;;13515:822;10801:29;10896:4;6810:700;;;;;1737:6:28;;;;249:4689:52;1737:6:28;:::i;:::-;249:4689:52;;;1869:3:28;249:4689:52;;1853:14:28;;;;;2126:23;1759:18;17992:82:37;;;;;2060:39:28;17992:82:37;;;;;;;;;;;;249:4689:52;;1131:12:28;249:4689:52;;;;;;;2060:39:28;:48;249:4689:52;;;2060:48:28;:::i;:::-;249:4689:52;;2126:23:28;:::i;:::-;1869:3;249:4689:52;1838:13:28;;;1853:14;2282:33;1853:14;;;;;;2282:33;1853:14;2282:33;249:4689:52;2282:33:28;:::i;:::-;;249:4689:52;;2282:33:28;-1:-1:-1;;;;;249:4689:52;;2340:16:28;;2336:796;;1833:331;7055:16:25;7051:453;;1833:331:28;6810:700:25;;;;:::o;7051:453::-;249:4689:52;;1759:18:28;7136:15:25;1759:18:28;;17992:82:37;;;7356:4:25;17992:82:37;;;;;;;735:10:39;249:4689:52;735:10:39;7356:4:25;:::i;:::-;7051:453;;;;;;7132:362;7474:4;735:10:39;249:4689:52;735:10:39;7474:4:25;:::i;:::-;7132:362;;2336:796:28;2372:26;249:4689:52;2372:26:28;;;249:4689:52;2412:497:28;2448:3;249:4689:52;;2432:14:28;;;;;1759:18;17992:82:37;;;;;2672:39:28;17992:82:37;;;;;;;;;;;;249:4689:52;;1131:12:28;249:4689:52;;;;;;;2672:39:28;249:4689:52;;;;;;;2448:3:28;249:4689:52;2417:13:28;;;2432:14;3074:33;2432:14;;;;;;;;2282:33;249:4689:52;;2282:33:28;249:4689:52;;3074:33:28;2336:796;;6810:700:25;;;;;1737:6:28;;;;;;:::i;:::-;-1:-1:-1;;;;;249:4689:52;;1759:18:28;1755:571;;6810:700:25;-1:-1:-1;;;;;249:4689:52;;2340:16:28;;2336:796;;6810:700:25;7055:16;7051:453;;6810:700;;;;;;:::o;7051:453::-;249:4689:52;;7150:1:25;7136:15;7150:1;;17992:82:37;;7356:4:25;17992:82:37;;;;;;735:10:39;;7356:4:25;:::i;:::-;7051:453;;;;;;;7132:362;7474:4;735:10:39;;;;7474:4:25;:::i;:::-;7132:362;;2336:796:28;2372:26;;1775:1;;2372:26;;1775:1;2448:3;249:4689:52;;2432:14:28;;;;;249:4689:52;17992:82:37;;;;;2672:39:28;17992:82:37;;;;;;;;;;;;249:4689:52;;1131:12:28;249:4689:52;;;;;;;2672:39:28;249:4689:52;;;;;;;2448:3:28;249:4689:52;2417:13:28;;;2432:14;3074:33;2432:14;;;;;;;3074:33;249:4689:52;;2282:33:28;249:4689:52;;3074:33:28;2336:796;;1755:571;1793:26;;1775:1;;;1869:3;249:4689:52;;1853:14:28;;;;;2126:23;249:4689:52;17992:82:37;;;;;2060:39:28;17992:82:37;;;;;;;;;;;;249:4689:52;;1131:12:28;249:4689:52;;;;;;;2126:23:28;1869:3;249:4689:52;1838:13:28;;;1853:14;2282:33;1853:14;;;2282:33;1853:14;;;;;2282:33;249:4689:52;2282:33:28;:::i;:::-;1755:571;;249:4689:52;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;249:4689:52;;;;:::o;:::-;;;:::o;2523:1028:30:-;;;;;;2747:14;;2743:802;;2523:1028;;;;;;;:::o;2743:802::-;2785:78;249:4689:52;2785:78:30;249:4689:52;;;;;;;;;;;2785:78:30;;;;;;:::i;:::-;;249:4689:52;2764:1:30;-1:-1:-1;;;;;249:4689:52;;2785:78:30;;2764:1;;2785:78;;;2743:802;-1:-1:-1;2781:754:30;;3147:388;;;:::i;:::-;249:4689:52;;;;3197:18:30;;;-1:-1:-1;;;2764:1:30;3302:41;-1:-1:-1;;;;;249:4689:52;;2785:78:30;249:4689:52;;2764:1:30;3302:41;3193:328;2785:78;3390:113;;;;2781:754;-1:-1:-1;;;;;;249:4689:52;-1:-1:-1;;;2942:60:30;2938:194;;2781:754;2743:802;;;;;;;;2938:194;-1:-1:-1;;;2764:1:30;3072:41;-1:-1:-1;;;;;249:4689:52;2785:78:30;249:4689:52;;2764:1:30;3302:41;2785:78;;;;;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;249:4689:52;-1:-1:-1;;;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;1006:961:30:-;;;;;;1205:14;;1201:760;;1006:961;;;;;;:::o;1201:760::-;1243:71;249:4689:52;1243:71:30;249:4689:52;;;;;;;;;;;1243:71:30;;;;;;:::i;:::-;;249:4689:52;1222:1:30;-1:-1:-1;;;;;249:4689:52;;1243:71:30;;1222:1;;1243:71;;;1201:760;-1:-1:-1;1239:712:30;;1563:388;;;:::i;1239:712::-;-1:-1:-1;;;;;;249:4689:52;-1:-1:-1;;;1363:55:30;1359:189;;1239:712;1201:760;;;;;;;;1243:71;;;;;;;;;;;;;;;:::i;:::-;;;;;249:4689:52;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;4950:1281:25:-;;;;249:4689:52;;;;5071:27:25;;;;5067:117;;5241:13;;-1:-1:-1;5272:3:25;249:4689:52;;5256:14:25;;;;;17992:82:37;;;;;;;;;;;;;;;;249:4689:52;;;;;;;;5402:420:25;;5272:3;249:4689:52;;-1:-1:-1;;;;;249:4689:52;;5836:81:25;;5272:3;;;;249:4689:52;5241:13:25;;5836:81;5876:26;:13;;:17;:13;5253:1;249:4689:52;5253:1:25;249:4689:52;;;5253:1:25;249:4689:52;;;5876:13:25;249:4689:52;;;;;;;;;;;;;;;;5876:17:25;249:4689:52;;;5876:26:25;:::i;:::-;249:4689:52;;5836:81:25;;;;;5402:420;5466:13;;;:19;:13;;;5253:1;249:4689:52;5253:1:25;249:4689:52;;;5253:1:25;249:4689:52;;;5466:19:25;249:4689:52;5507:19:25;;;5503:129;;249:4689:52;;;;;;;;5748:19:25;:13;;;5253:1;249:4689:52;5253:1:25;249:4689:52;;;5253:1:25;249:4689:52;;;5748:19:25;249:4689:52;5402:420:25;;;;5503:129;249:4689:52;;-1:-1:-1;;;5557:56:25;;-1:-1:-1;;;;;249:4689:52;;5557:56:25;;;249:4689:52;;;;;;;;;;;;;;;;;;;;;;5557:56:25;5256:14;-1:-1:-1;249:4689:52;;5256:14:25;;;;;;249:4689:52;5941:15:25;249:4689:52;;17992:82:37;;;;;;;;;249:4689:52;;;;;;;;;;-1:-1:-1;;;;;249:4689:52;;;;;;;;735:10:39;;6087:45:25;;249:4689:52;;;;6087:45:25;;;;4950:1281::o;5937:288::-;249:4689:52;;-1:-1:-1;;;;;249:4689:52;;;;;;;;735:10:39;;6168:46:25;;249:4689:52;;6168:46:25;;249:4689:52;6168:46:25;:::i;5067:117::-;-1:-1:-1;;;;5121:52:25;;249:4689:52;;;;2764:1:30;3302:41", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address,uint256)": "00fdd58e", "balanceOfBatch(address[],uint256[])": "4e1273f4", "createNewToken(address,uint256,string,string,uint256,bytes)": "c885bbb8", "exists(uint256)": "4f558e79", "getCurrentTokenId()": "56189236", "getMaxSupply(uint256)": "5e495d74", "getRemainingSupply(uint256)": "8c858a14", "getTokenMetadata(uint256)": "60316801", "isApprovedForAll(address,address)": "e985e9c5", "mint(address,uint256,uint256,string,string,uint256,bytes)": "d19eac5f", "mintBatch(address,uint256[],uint256[],string[],string[],uint256[],bytes)": "70b46dd9", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)": "2eb2c2d6", "safeTransferFrom(address,address,uint256,uint256,bytes)": "f242432a", "setApprovalForAll(address,bool)": "a22cb465", "supportsInterface(bytes4)": "01ffc9a7", "totalSupply()": "18160ddd", "totalSupply(uint256)": "bd85b039", "transferOwnership(address)": "f2fde38b", "updateTokenMetadata(uint256,string)": "2cb2f52e", "updateTokenURI(uint256,string)": "18e97fd1", "uri(uint256)": "0e89341c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"baseURI\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC1155InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"idsLength\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"valuesLength\",\"type\":\"uint256\"}],\"name\":\"ERC1155InvalidArrayLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidOperator\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC1155InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC1155MissingApprovalForAll\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"values\",\"type\":\"uint256[]\"}],\"name\":\"TransferBatch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"TransferSingle\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"URI\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"}],\"name\":\"balanceOfBatch\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"tokenURI\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"metadata\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"maxSupply\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"createNewToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"exists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentTokenId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getMaxSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getRemainingSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getTokenMetadata\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"tokenURI\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"metadata\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"maxSupply\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"string[]\",\"name\":\"tokenURIs\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"metadatas\",\"type\":\"string[]\"},{\"internalType\":\"uint256[]\",\"name\":\"maxSupplies\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"mintBatch\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"values\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeBatchTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"metadata\",\"type\":\"string\"}],\"name\":\"updateTokenMetadata\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"newURI\",\"type\":\"string\"}],\"name\":\"updateTokenURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"uri\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC1155InsufficientBalance(address,uint256,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC1155InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC1155InvalidArrayLength(uint256,uint256)\":[{\"details\":\"Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation. Used in batch transfers.\",\"params\":{\"idsLength\":\"Length of the array of token identifiers\",\"valuesLength\":\"Length of the array of token amounts\"}}],\"ERC1155InvalidOperator(address)\":[{\"details\":\"Indicates a failure with the `operator` to be approved. Used in approvals.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC1155InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC1155InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC1155MissingApprovalForAll(address,address)\":[{\"details\":\"Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\",\"owner\":\"Address of the current owner of a token.\"}}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"ApprovalForAll(address,address,bool)\":{\"details\":\"Emitted when `account` grants or revokes permission to `operator` to transfer their tokens, according to `approved`.\"},\"TransferBatch(address,address,address,uint256[],uint256[])\":{\"details\":\"Equivalent to multiple {TransferSingle} events, where `operator`, `from` and `to` are the same for all transfers.\"},\"TransferSingle(address,address,address,uint256,uint256)\":{\"details\":\"Emitted when `value` amount of tokens of type `id` are transferred from `from` to `to` by `operator`.\"},\"URI(string,uint256)\":{\"details\":\"Emitted when the URI for token type `id` changes to `value`, if it is a non-programmatic URI. If an {URI} event was emitted for `id`, the standard https://eips.ethereum.org/EIPS/eip-1155#metadata-extensions[guarantees] that `value` will equal the value returned by {IERC1155MetadataURI-uri}.\"}},\"kind\":\"dev\",\"methods\":{\"balanceOf(address,uint256)\":{\"details\":\"Returns the value of tokens of token type `id` owned by `account`.\"},\"balanceOfBatch(address[],uint256[])\":{\"details\":\"See {IERC1155-balanceOfBatch}. Requirements: - `accounts` and `ids` must have the same length.\"},\"isApprovedForAll(address,address)\":{\"details\":\"Returns true if `operator` is approved to transfer ``account``'s tokens. See {setApprovalForAll}.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)\":{\"details\":\"xref:ROOT:erc1155.adoc#batch-operations[Batched] version of {safeTransferFrom}. WARNING: This function can potentially allow a reentrancy attack when transferring tokens to an untrusted contract, when invoking {IERC1155Receiver-onERC1155BatchReceived} on the receiver. Ensure to follow the checks-effects-interactions pattern and consider employing reentrancy guards when interacting with untrusted contracts. Emits either a {TransferSingle} or a {TransferBatch} event, depending on the length of the array arguments. Requirements: - `ids` and `values` must have the same length. - If `to` refers to a smart contract, it must implement {IERC1155Receiver-onERC1155BatchReceived} and return the acceptance magic value.\"},\"safeTransferFrom(address,address,uint256,uint256,bytes)\":{\"details\":\"Transfers a `value` amount of tokens of type `id` from `from` to `to`. WARNING: This function can potentially allow a reentrancy attack when transferring tokens to an untrusted contract, when invoking {IERC1155Receiver-onERC1155Received} on the receiver. Ensure to follow the checks-effects-interactions pattern and consider employing reentrancy guards when interacting with untrusted contracts. Emits a {TransferSingle} event. Requirements: - `to` cannot be the zero address. - If the caller is not `from`, it must have been approved to spend ``from``'s tokens via {setApprovalForAll}. - `from` must have a balance of tokens of type `id` of at least `value` amount. - If `to` refers to a smart contract, it must implement {IERC1155Receiver-onERC1155Received} and return the acceptance magic value.\"},\"setApprovalForAll(address,bool)\":{\"details\":\"Grants or revokes permission to `operator` to transfer the caller's tokens, according to `approved`, Emits an {ApprovalForAll} event. Requirements: - `operator` cannot be the zero address.\"},\"supportsInterface(bytes4)\":{\"details\":\"Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section] to learn more about how these ids are created. This function call must use less than 30 000 gas.\"},\"totalSupply()\":{\"details\":\"Total value of tokens.\"},\"totalSupply(uint256)\":{\"details\":\"Total value of tokens in with a given id.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Scan2EarnERC1155.sol\":\"Scan2EarnERC1155\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol\":{\"keccak256\":\"0xb90916a13c108291699c6e6ddb4b1277ea860dfac118a60568a6f432baedcf68\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://90719e236eafdcbdf56d830f460714668f95eb76c534edf140bb2948d9f8a048\",\"dweb:/ipfs/QmU8QmdKUHBejNeYGuJ913L7xhcysyr5MZLnNnPy9Jqrdx\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol\":{\"keccak256\":\"0xf189f9b417fe1931e1ab706838aff1128528694a9fcdb5ff7665197f2ca57d09\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ff0143c836c8c9f85d13708733c09e21251395847fccfb518bf3b556726a840\",\"dweb:/ipfs/QmP69sjjrQrhYAsvCSSB69Bx66SiUPdQUqdzMYnf4wANHm\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol\":{\"keccak256\":\"0x6ec6d7fce29668ede560c7d2e10f9d10de3473f5298e431e70a5767db42fa620\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac0139e51874aeec0730d040e57993187541777eb01d5939c06d5d2b986a54e8\",\"dweb:/ipfs/QmZbMbdPzusXuX9FGkyArV8hgzKLBZaL5RzMtCdCawtwPF\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol\":{\"keccak256\":\"0xfb3846932e2bcdc16d400502e89a452551eaf522e3db3e4de634d6bc167500c1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc3b4ca11ea0e46816996b888042dd400217596fbfe53c54a0a41c51979c35c4\",\"dweb:/ipfs/QmanCqi8fhxUi2KfL5pnkqFnYacPNRV8gEXrfB6nozEPjV\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol\":{\"keccak256\":\"0x98c32de9b02f43eba7c0aba9fadf331cffb35a8d2076dce1d556c8f2cad704aa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://38f68f76e741cce3ca4e0f8ece5ab9d69a203829311a403bafc8f7b95a7e6d63\",\"dweb:/ipfs/QmZ8PrDXU5DiZ7fzEoRN7vHMQdemtsqvYVV5AdvAnScS4o\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol\":{\"keccak256\":\"0xaeca1511f7da49bdb16e44aa0f09dca76b51ba079bd068b2f80b8fe6d22b5fa1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://542fe084e72e14f4298954f70c407d5795c1207a02c049f9b91e15f3b9a525a5\",\"dweb:/ipfs/QmbPQMVUVZBE9R9Va6FNfZBTrUY51nrjm8qxpeoAoRrPrQ\"]},\"lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0xa4b9958797e0e9cde82a090525e90f80d5745ba1c67ee72b488bd3087498a17e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9344f7c2f80322c2e76d9d89bed03fd12f3e011e74fde7cf24ea21bdd2abe2d\",\"dweb:/ipfs/QmPMAjF5x2fHUAee2FKMZDBbFVrbZbPCr3a9KHLZaSn1zY\"]},\"lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x2d9dc2fe26180f74c11c13663647d38e259e45f95eb88f57b61d2160b0109d3e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://81233d1f98060113d9922180bb0f14f8335856fe9f339134b09335e9f678c377\",\"dweb:/ipfs/QmWh6R35SarhAn4z2wH8SU456jJSYL2FgucfTFgbHJJN4E\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"src/Scan2EarnERC1155.sol\":{\"keccak256\":\"0x19387293a9dc5aba80d7417539e960455963418f12b44fe44c91a2745e8833d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2a8e1db9e6848d319d4e331f7ebdac8b884ff8b2047de60e7aea27686823786d\",\"dweb:/ipfs/QmbYUFGUmNdZtmZPJRp4Ft6iAFkzyrt9f4uDgmfc7PZ8ce\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "baseURI", "type": "string"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "type": "error", "name": "ERC1155InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC1155InvalidApprover"}, {"inputs": [{"internalType": "uint256", "name": "idsLength", "type": "uint256"}, {"internalType": "uint256", "name": "valuesLength", "type": "uint256"}], "type": "error", "name": "ERC1155InvalidArrayLength"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "type": "error", "name": "ERC1155InvalidOperator"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC1155InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC1155InvalidSender"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC1155MissingApprovalForAll"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approved", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]", "indexed": false}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]", "indexed": false}], "type": "event", "name": "TransferBatch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "id", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "TransferSingle", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "value", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "id", "type": "uint256", "indexed": true}], "type": "event", "name": "URI", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}], "stateMutability": "view", "type": "function", "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "tokenURI", "type": "string"}, {"internalType": "string", "name": "metadata", "type": "string"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "createNewToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "exists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentTokenId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getMaxSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRemainingSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getTokenMetadata", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "tokenURI", "type": "string"}, {"internalType": "string", "name": "metadata", "type": "string"}, {"internalType": "uint256", "name": "maxSupply", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "string[]", "name": "tokenURIs", "type": "string[]"}, {"internalType": "string[]", "name": "metadatas", "type": "string[]"}, {"internalType": "uint256[]", "name": "maxSupplies", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "mintBatch"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeBatchTransferFrom"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "string", "name": "metadata", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "updateTokenMetadata"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "string", "name": "newURI", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "updateTokenURI"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "uri", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}], "devdoc": {"kind": "dev", "methods": {"balanceOf(address,uint256)": {"details": "Returns the value of tokens of token type `id` owned by `account`."}, "balanceOfBatch(address[],uint256[])": {"details": "See {IERC1155-balanceOfBatch}. Requirements: - `accounts` and `ids` must have the same length."}, "isApprovedForAll(address,address)": {"details": "Returns true if `operator` is approved to transfer ``account``'s tokens. See {setApprovalForAll}."}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)": {"details": "xref:ROOT:erc1155.adoc#batch-operations[Batched] version of {safeTransferFrom}. WARNING: This function can potentially allow a reentrancy attack when transferring tokens to an untrusted contract, when invoking {IERC1155Receiver-onERC1155BatchReceived} on the receiver. Ensure to follow the checks-effects-interactions pattern and consider employing reentrancy guards when interacting with untrusted contracts. Emits either a {TransferSingle} or a {TransferBatch} event, depending on the length of the array arguments. Requirements: - `ids` and `values` must have the same length. - If `to` refers to a smart contract, it must implement {IERC1155Receiver-onERC1155BatchReceived} and return the acceptance magic value."}, "safeTransferFrom(address,address,uint256,uint256,bytes)": {"details": "Transfers a `value` amount of tokens of type `id` from `from` to `to`. WARNING: This function can potentially allow a reentrancy attack when transferring tokens to an untrusted contract, when invoking {IERC1155Receiver-onERC1155<PERSON><PERSON>ei<PERSON>} on the receiver. Ensure to follow the checks-effects-interactions pattern and consider employing reentrancy guards when interacting with untrusted contracts. Emits a {TransferSingle} event. Requirements: - `to` cannot be the zero address. - If the caller is not `from`, it must have been approved to spend ``from``'s tokens via {setApprovalForAll}. - `from` must have a balance of tokens of type `id` of at least `value` amount. - If `to` refers to a smart contract, it must implement {IERC1155Receiver-onERC1155Received} and return the acceptance magic value."}, "setApprovalForAll(address,bool)": {"details": "Grants or revokes permission to `operator` to transfer the caller's tokens, according to `approved`, Emits an {ApprovalForAll} event. Requirements: - `operator` cannot be the zero address."}, "supportsInterface(bytes4)": {"details": "Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section] to learn more about how these ids are created. This function call must use less than 30 000 gas."}, "totalSupply()": {"details": "Total value of tokens."}, "totalSupply(uint256)": {"details": "Total value of tokens in with a given id."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Scan2EarnERC1155.sol": "Scan2EarnERC1155"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol": {"keccak256": "0xb90916a13c108291699c6e6ddb4b1277ea860dfac118a60568a6f432baedcf68", "urls": ["bzz-raw://90719e236eafdcbdf56d830f460714668f95eb76c534edf140bb2948d9f8a048", "dweb:/ipfs/QmU8QmdKUHBejNeYGuJ913L7xhcysyr5MZLnNnPy9Jqrdx"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol": {"keccak256": "0xf189f9b417fe1931e1ab706838aff1128528694a9fcdb5ff7665197f2ca57d09", "urls": ["bzz-raw://2ff0143c836c8c9f85d13708733c09e21251395847fccfb518bf3b556726a840", "dweb:/ipfs/QmP69sjjrQrhYAsvCSSB69Bx66SiUPdQUqdzMYnf4wANHm"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol": {"keccak256": "0x6ec6d7fce29668ede560c7d2e10f9d10de3473f5298e431e70a5767db42fa620", "urls": ["bzz-raw://ac0139e51874aeec0730d040e57993187541777eb01d5939c06d5d2b986a54e8", "dweb:/ipfs/QmZbMbdPzusXuX9FGkyArV8hgzKLBZaL5RzMtCdCawtwPF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol": {"keccak256": "0xfb3846932e2bcdc16d400502e89a452551eaf522e3db3e4de634d6bc167500c1", "urls": ["bzz-raw://bc3b4ca11ea0e46816996b888042dd400217596fbfe53c54a0a41c51979c35c4", "dweb:/ipfs/QmanCqi8fhxUi2KfL5pnkqFnYacPNRV8gEXrfB6nozEPjV"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol": {"keccak256": "0x98c32de9b02f43eba7c0aba9fadf331cffb35a8d2076dce1d556c8f2cad704aa", "urls": ["bzz-raw://38f68f76e741cce3ca4e0f8ece5ab9d69a203829311a403bafc8f7b95a7e6d63", "dweb:/ipfs/QmZ8PrDXU5DiZ7fzEoRN7vHMQdemtsqvYVV5AdvAnScS4o"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol": {"keccak256": "0xaeca1511f7da49bdb16e44aa0f09dca76b51ba079bd068b2f80b8fe6d22b5fa1", "urls": ["bzz-raw://542fe084e72e14f4298954f70c407d5795c1207a02c049f9b91e15f3b9a525a5", "dweb:/ipfs/QmbPQMVUVZBE9R9Va6FNfZBTrUY51nrjm8qxpeoAoRrPrQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0xa4b9958797e0e9cde82a090525e90f80d5745ba1c67ee72b488bd3087498a17e", "urls": ["bzz-raw://c9344f7c2f80322c2e76d9d89bed03fd12f3e011e74fde7cf24ea21bdd2abe2d", "dweb:/ipfs/QmPMAjF5x2fHUAee2FKMZDBbFVrbZbPCr3a9KHLZaSn1zY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x2d9dc2fe26180f74c11c13663647d38e259e45f95eb88f57b61d2160b0109d3e", "urls": ["bzz-raw://81233d1f98060113d9922180bb0f14f8335856fe9f339134b09335e9f678c377", "dweb:/ipfs/QmWh6R35SarhAn4z2wH8SU456jJSYL2FgucfTFgbHJJN4E"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "src/Scan2EarnERC1155.sol": {"keccak256": "0x19387293a9dc5aba80d7417539e960455963418f12b44fe44c91a2745e8833d3", "urls": ["bzz-raw://2a8e1db9e6848d319d4e331f7ebdac8b884ff8b2047de60e7aea27686823786d", "dweb:/ipfs/QmbYUFGUmNdZtmZPJRp4Ft6iAFkzyrt9f4uDgmfc7PZ8ce"], "license": "MIT"}}, "version": 1}, "id": 52}