{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/Base.sol\":\"ScriptBase\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c\",\"dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/Base.sol": "ScriptBase"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xaae88142a348064677cf81169ea0ca706be3e99e2a0f13a01878ff8d52cf6b25", "urls": ["bzz-raw://da4fb0ed1b31381c7e60ee386acc72796e8a8325c83739ec9a3678e2f841073c", "dweb:/ipfs/Qmb4MU6FpAi8Wt5kUQ3wa7ooHnfMVbdd9zuvnqbTehLmxJ"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "id": 0}