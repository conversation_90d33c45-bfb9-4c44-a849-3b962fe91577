{"abi": [{"type": "function", "name": "aggregate", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "returnData", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "payable"}, {"type": "function", "name": "aggregate3", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call3[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "allowFailure", "type": "bool", "internalType": "bool"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "aggregate3Value", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call3Value[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "allowFailure", "type": "bool", "internalType": "bool"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "blockAndAggregate", "inputs": [{"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "getBasefee", "inputs": [], "outputs": [{"name": "basefee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockHash", "inputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockNumber", "inputs": [], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "get<PERSON>hainId", "inputs": [], "outputs": [{"name": "chainid", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockCoinbase", "inputs": [], "outputs": [{"name": "coinbase", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockDifficulty", "inputs": [], "outputs": [{"name": "difficulty", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockGasLimit", "inputs": [], "outputs": [{"name": "gaslimit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentBlockTimestamp", "inputs": [], "outputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getEthBalance", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getLastBlockHash", "inputs": [], "outputs": [{"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "tryAggregate", "inputs": [{"name": "requireSuccess", "type": "bool", "internalType": "bool"}, {"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}, {"type": "function", "name": "tryBlockAndAggregate", "inputs": [{"name": "requireSuccess", "type": "bool", "internalType": "bool"}, {"name": "calls", "type": "tuple[]", "internalType": "struct IMulticall3.Call[]", "components": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "returnData", "type": "tuple[]", "internalType": "struct IMulticall3.Result[]", "components": [{"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "payable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"aggregate((address,bytes)[])": "252dba42", "aggregate3((address,bool,bytes)[])": "82ad56cb", "aggregate3Value((address,bool,uint256,bytes)[])": "174dea71", "blockAndAggregate((address,bytes)[])": "c3077fa9", "getBasefee()": "3e64a696", "getBlockHash(uint256)": "ee82ac5e", "getBlockNumber()": "42cbb15c", "getChainId()": "3408e470", "getCurrentBlockCoinbase()": "a8b0574e", "getCurrentBlockDifficulty()": "72425d9d", "getCurrentBlockGasLimit()": "86d516e8", "getCurrentBlockTimestamp()": "0f28c97d", "getEthBalance(address)": "4d2301cc", "getLastBlockHash()": "27e86d6e", "tryAggregate(bool,(address,bytes)[])": "bce38bd7", "tryBlockAndAggregate(bool,(address,bytes)[])": "399542e9"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes[]\",\"name\":\"returnData\",\"type\":\"bytes[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowFailure\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call3[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate3\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"allowFailure\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call3Value[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"aggregate3Value\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"blockAndAggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBasefee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"basefee\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"}],\"name\":\"getBlockHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockNumber\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getChainId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"chainid\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockCoinbase\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"coinbase\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockDifficulty\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"difficulty\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockGasLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"gaslimit\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"getEthBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLastBlockHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"requireSuccess\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"tryAggregate\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"requireSuccess\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Call[]\",\"name\":\"calls\",\"type\":\"tuple[]\"}],\"name\":\"tryBlockAndAggregate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blockNumber\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"returnData\",\"type\":\"bytes\"}],\"internalType\":\"struct IMulticall3.Result[]\",\"name\":\"returnData\",\"type\":\"tuple[]\"}],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IMulticall3.sol\":\"IMulticall3\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes[]", "name": "returnData", "type": "bytes[]"}]}, {"inputs": [{"internalType": "struct IMulticall3.Call3[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bool", "name": "allowFailure", "type": "bool"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate3", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "struct IMulticall3.Call3Value[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bool", "name": "allowFailure", "type": "bool"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "aggregate3Value", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "blockAndAggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBasefee", "outputs": [{"internalType": "uint256", "name": "basefee", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getBlockHash", "outputs": [{"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockNumber", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "get<PERSON>hainId", "outputs": [{"internalType": "uint256", "name": "chainid", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockCoinbase", "outputs": [{"internalType": "address", "name": "coinbase", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockDifficulty", "outputs": [{"internalType": "uint256", "name": "difficulty", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockGasLimit", "outputs": [{"internalType": "uint256", "name": "gaslimit", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getEthBalance", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getLastBlockHash", "outputs": [{"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}]}, {"inputs": [{"internalType": "bool", "name": "requireSuccess", "type": "bool"}, {"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "tryAggregate", "outputs": [{"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "bool", "name": "requireSuccess", "type": "bool"}, {"internalType": "struct IMulticall3.Call[]", "name": "calls", "type": "tuple[]", "components": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}]}], "stateMutability": "payable", "type": "function", "name": "tryBlockAndAggregate", "outputs": [{"internalType": "uint256", "name": "blockNumber", "type": "uint256"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "struct IMulticall3.Result[]", "name": "returnData", "type": "tuple[]", "components": [{"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "returnData", "type": "bytes"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IMulticall3.sol": "IMulticall3"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}}, "version": 1}, "id": 18}