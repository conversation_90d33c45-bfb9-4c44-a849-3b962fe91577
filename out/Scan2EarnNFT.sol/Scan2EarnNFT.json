{"abi": [{"type": "constructor", "inputs": [{"name": "initialOwner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "collections", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "nftContract", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "expirationTime", "type": "uint256", "internalType": "uint256"}, {"name": "maxMintable", "type": "uint256", "internalType": "uint256"}, {"name": "currentMinted", "type": "uint256", "internalType": "uint256"}, {"name": "mediaUrlCount", "type": "uint8", "internalType": "uint8"}, {"name": "title", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "nftType", "type": "uint8", "internalType": "enum Scan2EarnNFT.NFTType"}, {"name": "isActive", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "contractToCollectionId", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "createCollection", "inputs": [{"name": "_nftContract", "type": "address", "internalType": "address"}, {"name": "_name", "type": "string", "internalType": "string"}, {"name": "_expirationTime", "type": "uint256", "internalType": "uint256"}, {"name": "_maxMintable", "type": "uint256", "internalType": "uint256"}, {"name": "_mediaUrls", "type": "string[]", "internalType": "string[]"}, {"name": "_mediaTypes", "type": "string[]", "internalType": "string[]"}, {"name": "_title", "type": "string", "internalType": "string"}, {"name": "_description", "type": "string", "internalType": "string"}, {"name": "_nftType", "type": "uint8", "internalType": "enum Scan2EarnNFT.NFTType"}, {"name": "_attributeNames", "type": "string[]", "internalType": "string[]"}, {"name": "_attributeValues", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getCollection", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "nftContract", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "expirationTime", "type": "uint256", "internalType": "uint256"}, {"name": "maxMintable", "type": "uint256", "internalType": "uint256"}, {"name": "currentMinted", "type": "uint256", "internalType": "uint256"}, {"name": "title", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "nftType", "type": "uint8", "internalType": "enum Scan2EarnNFT.NFTType"}, {"name": "isActive", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getCollectionAttributes", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct Scan2EarnNFT.Attribute[]", "components": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "value", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCollectionMediaUrls", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct Scan2EarnNFT.MediaUrl[]", "components": [{"name": "url", "type": "string", "internalType": "string"}, {"name": "mediaType", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentCollectionId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRemainingMintable", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isCollectionExpired", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mintNFT", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateCollectionStatus", "inputs": [{"name": "_collectionId", "type": "uint256", "internalType": "uint256"}, {"name": "_isActive", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "CollectionCreated", "inputs": [{"name": "collectionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "nftContract", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "nftType", "type": "uint8", "indexed": false, "internalType": "enum Scan2EarnNFT.NFTType"}], "anonymous": false}, {"type": "event", "name": "NFTMinted", "inputs": [{"name": "collectionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "290:6651:54:-:0;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;1273:26:20;;1269:95;;-1:-1:-1;290:6651:54;;-1:-1:-1;;;;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;3052:40:20;;-1:-1:-1;3052:40:20;1857:1:41;290:6651:54;;1857:1:41;1644:21:54;290:6651;;;;;;;;1269:95:20;1322:31;;;-1:-1:-1;1322:31:20;-1:-1:-1;1322:31:20;290:6651:54;;-1:-1:-1;1322:31:20;290:6651:54;-1:-1:-1;290:6651:54;;;;;;-1:-1:-1;290:6651:54;;;;;-1:-1:-1;290:6651:54", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "290:6651:54:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;;;;1130:52;290:6651;;;;;;;;;;;1130:52;;290:6651;1130:52;;;:::i;:::-;290:6651;;1130:52;290:6651;1130:52;;;290:6651;1130:52;;;290:6651;;1130:52;;290:6651;;1130:52;;;290:6651;;1130:52;;;;;;:::i;:::-;;290:6651;;1130:52;;;;;;;;:::i;:::-;;;290:6651;;1130:52;;290:6651;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;:::i;:::-;1500:62:20;;:::i;:::-;-1:-1:-1;;;;;290:6651:54;2627:22:20;;2623:91;;290:6651:54;;-1:-1:-1;;;;;;290:6651:54;;;;;;-1:-1:-1;;;;;290:6651:54;3052:40:20;290:6651:54;;3052:40:20;290:6651:54;;2623:91:20;-1:-1:-1;;;2672:31:20;;290:6651:54;;;;;2672:31:20;;290:6651:54;;;;;;;-1:-1:-1;;290:6651:54;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;;;;;1500:62:20;;:::i;:::-;1899:1:41;290:6651:54;;2702:18:41;2698:86;;1899:1;290:6651:54;;;;;4113:11;290:6651;;;;;;4166:19;;;290:6651;;;;;1899:1:41;4250:25:54;;290:6651;4232:15;:43;290:6651;;;;4320:24;;290:6651;4320:34;290:6651;;;4320:34;:::i;:::-;4113:11;4358:22;;290:6651;-1:-1:-1;290:6651:54;;;4428:18;;;290:6651;;1899:1:41;290:6651:54;;;;;4428:36;;290:6651;4488:12;;290:6651;;;;;-1:-1:-1;;;;;290:6651:54;4557:73;;;;;290:6651;;;;;;;;;;;;;4557:73;;4609:4;290:6651;4557:73;;290:6651;;;;;;;;;;;4557:73;;;;;;;;4424:339;;;;4840:48;4424:339;290:6651;4424:339;;4781:35;290:6651;;;4781:35;:::i;:::-;290:6651;;;;;;;;;;;4840:48;290:6651;;;;;4557:73;;;;;;;;:::i;:::-;290:6651;;4557:73;;;;;290:6651;;;;4557:73;290:6651;;;;;;;;;4557:73;290:6651;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;4424:339;290:6651;;;;-1:-1:-1;;;;;290:6651:54;4661:91;;;;;290:6651;;;4661:91;290:6651;;;;;;;;;4661:91;;4718:4;290:6651;4661:91;;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;4661:91;;;;;;;;4424:339;;;290:6651;4424:339;4840:48;4424:339;;;4661:91;4840:48;4661:91;;;;;;290:6651;;4661:91;;:::i;:::-;290:6651;4661:91;;;;;;;;290:6651;;;;;;;;;4661:91;290:6651;;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;;;;2698:86:41;2743:30;;;290:6651:54;2743:30:41;290:6651:54;;2743:30:41;290:6651:54;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;5807:11;290:6651;;;;;;5883:24;;;290:6651;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;;290:6651:54;;;:::i;:::-;;;;;;;;;5932:11;;6004:20;290:6651;6004:20;;5927:111;5975:3;290:6651;;;5945:28;;;;;;6004:23;5994:33;290:6651;6004:23;290:6651;6004:23;290:6651;6004:23;;;:::i;:::-;290:6651;;:::i;:::-;5994:33;;;;:::i;:::-;;;;:::i;:::-;;290:6651;;5932:11;;5945:28;;290:6651;;;;;;;;;;;;;;;;;;;;;;6004:20;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;:::i;:::-;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;:::i;:::-;1500:62:20;;;:::i;:::-;-1:-1:-1;;;;;290:6651:54;;2139:26;;290:6651;;2230:15;2212:33;;290:6651;;;2301:16;;290:6651;;2396:2;290:6651;;2375:23;290:6651;;;;;;2440:39;290:6651;;;;;;2538:49;290:6651;;;;;-1:-1:-1;;290:6651:54;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;;;;;;;;2815:15;;290:6651;;-1:-1:-1;;;;;290:6651:54;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;2848:25;;290:6651;2733:11;2901:22;;290:6651;-1:-1:-1;290:6651:54;2948:24;;290:6651;;;2986:16;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;3021:22;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;;3068:18;;;290:6651;;;;;;;;;;;3107:19;;;290:6651;;;;;;;;;;;;;3143:24;;;290:6651;;;;;;;;;;3299:20;;;;3239:200;3280:3;290:6651;;;;;;3257:21;;;;;;3399:14;3357:13;;;;:::i;:::-;;3399:14;;;:::i;:::-;;290:6651;;;;;;:::i;:::-;;;;3325:103;;290:6651;3299:23;;;;:::i;:::-;290:6651;;;;;;;:::i;:::-;;;;;;;;;3244:11;;290:6651;;;;;;;;;;;;;;;;;;;;;;;;3257:21;-1:-1:-1;3257:21:54;;3550;;;-1:-1:-1;3257:21:54;;290:6651;3531:3;290:6651;;3503:26;;;;;3611:18;;;;:::i;:::-;;3654:19;;;;;:::i;:::-;;290:6651;;;;;;:::i;:::-;;;;3577:111;;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;3488:13;;290:6651;;;;;;;;;;;;;;;;;;;;;;;;3503:26;290:6651;3503:26;;;3793:62;290:6651;3503:26;;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;3793:62;;;290:6651;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2733:11;290:6651;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;-1:-1:-1;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;6501:17;290:6651;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;-1:-1:-1;;;;;290:6651:54;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;1500:62:20;;:::i;:::-;290:6651:54;;;-1:-1:-1;;;;;;290:6651:54;;;;-1:-1:-1;;;;;290:6651:54;3052:40:20;290:6651:54;;3052:40:20;290:6651:54;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;6652:11;290:6651;;;6652:41;290:6651;;;6652:41;290:6651;6633:15;:60;;290:6651;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;5286:11;290:6651;;;;;;;;;;;;;;5408:25;;;;290:6651;;;5286:11;5447:22;;290:6651;;;5483:24;;290:6651;5587:18;290:6651;5587:18;;;290:6651;;5619:19;290:6651;5619:19;;;290:6651;;5379:15;290:6651;;5379:15;;290:6651;:::i;:::-;5521:16;290:6651;;5551:22;290:6651;5521:16;;;290:6651;:::i;:::-;5551:22;;290:6651;:::i;:::-;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;;;;;;;1500:62:20;;:::i;:::-;290:6651:54;;;;6354:11;290:6651;;6354:35;290:6651;;;6354:35;290:6651;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;6840:11;290:6651;;;;;;6840:11;6883:22;;290:6651;6908:24;;290:6651;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;;;;6200:11;290:6651;;6200:37;290:6651;;;6200:37;290:6651;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;-1:-1:-1;;290:6651:54;;;;:::o;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;:::o;:::-;;;;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;290:6651:54;;;;;;:::o;:::-;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;:::o;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;:::o;:::-;;;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;-1:-1:-1;;290:6651:54;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;;;;290:6651:54;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:::o;:::-;;;-1:-1:-1;290:6651:54;;;;;-1:-1:-1;290:6651:54;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;290:6651:54;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;290:6651:54;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;-1:-1:-1;290:6651:54;;;;;;;;;;;;;;:::o;1796:162:20:-;1710:6;290:6651:54;-1:-1:-1;;;;;290:6651:54;735:10:39;1855:23:20;1851:101;;1796:162::o;1851:101::-;1901:40;;;1710:6;1901:40;735:10:39;1901:40:20;290:6651:54;;1710:6:20;1901:40", "linkReferences": {}}, "methodIdentifiers": {"collections(uint256)": "fdbda0ec", "contractToCollectionId(address)": "79aa4640", "createCollection(address,string,uint256,uint256,string[],string[],string,string,uint8,string[],string[])": "8ca4b690", "getCollection(uint256)": "5a1f3c28", "getCollectionAttributes(uint256)": "1b5a8a57", "getCollectionMediaUrls(uint256)": "99eeb050", "getCurrentCollectionId()": "8be39fe9", "getRemainingMintable(uint256)": "40cc0cac", "isCollectionExpired(uint256)": "607e9801", "mintNFT(uint256,address,uint256,uint256)": "ed55979e", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b", "updateCollectionStatus(uint256,bool)": "4646ae73"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"collectionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"nftContract\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"enum Scan2EarnNFT.NFTType\",\"name\":\"nftType\",\"type\":\"uint8\"}],\"name\":\"CollectionCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"collectionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"NFTMinted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"collections\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"nftContract\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"expirationTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxMintable\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"currentMinted\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"mediaUrlCount\",\"type\":\"uint8\"},{\"internalType\":\"string\",\"name\":\"title\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"enum Scan2EarnNFT.NFTType\",\"name\":\"nftType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isActive\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"contractToCollectionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_nftContract\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_expirationTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_maxMintable\",\"type\":\"uint256\"},{\"internalType\":\"string[]\",\"name\":\"_mediaUrls\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"_mediaTypes\",\"type\":\"string[]\"},{\"internalType\":\"string\",\"name\":\"_title\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_description\",\"type\":\"string\"},{\"internalType\":\"enum Scan2EarnNFT.NFTType\",\"name\":\"_nftType\",\"type\":\"uint8\"},{\"internalType\":\"string[]\",\"name\":\"_attributeNames\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"_attributeValues\",\"type\":\"string[]\"}],\"name\":\"createCollection\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"}],\"name\":\"getCollection\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"nftContract\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"expirationTime\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxMintable\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"currentMinted\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"title\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"enum Scan2EarnNFT.NFTType\",\"name\":\"nftType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isActive\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"}],\"name\":\"getCollectionAttributes\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"internalType\":\"struct Scan2EarnNFT.Attribute[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"}],\"name\":\"getCollectionMediaUrls\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"url\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"mediaType\",\"type\":\"string\"}],\"internalType\":\"struct Scan2EarnNFT.MediaUrl[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentCollectionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"}],\"name\":\"getRemainingMintable\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"}],\"name\":\"isCollectionExpired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_tokenId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"mintNFT\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_collectionId\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"_isActive\",\"type\":\"bool\"}],\"name\":\"updateCollectionStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/Scan2EarnNFT.sol\":\"Scan2EarnNFT\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23\",\"dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol\":{\"keccak256\":\"0xb90916a13c108291699c6e6ddb4b1277ea860dfac118a60568a6f432baedcf68\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://90719e236eafdcbdf56d830f460714668f95eb76c534edf140bb2948d9f8a048\",\"dweb:/ipfs/QmU8QmdKUHBejNeYGuJ913L7xhcysyr5MZLnNnPy9Jqrdx\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol\":{\"keccak256\":\"0xf189f9b417fe1931e1ab706838aff1128528694a9fcdb5ff7665197f2ca57d09\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ff0143c836c8c9f85d13708733c09e21251395847fccfb518bf3b556726a840\",\"dweb:/ipfs/QmP69sjjrQrhYAsvCSSB69Bx66SiUPdQUqdzMYnf4wANHm\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol\":{\"keccak256\":\"0x6ec6d7fce29668ede560c7d2e10f9d10de3473f5298e431e70a5767db42fa620\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac0139e51874aeec0730d040e57993187541777eb01d5939c06d5d2b986a54e8\",\"dweb:/ipfs/QmZbMbdPzusXuX9FGkyArV8hgzKLBZaL5RzMtCdCawtwPF\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol\":{\"keccak256\":\"0x98c32de9b02f43eba7c0aba9fadf331cffb35a8d2076dce1d556c8f2cad704aa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://38f68f76e741cce3ca4e0f8ece5ab9d69a203829311a403bafc8f7b95a7e6d63\",\"dweb:/ipfs/QmZ8PrDXU5DiZ7fzEoRN7vHMQdemtsqvYVV5AdvAnScS4o\"]},\"lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol\":{\"keccak256\":\"0xaeca1511f7da49bdb16e44aa0f09dca76b51ba079bd068b2f80b8fe6d22b5fa1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://542fe084e72e14f4298954f70c407d5795c1207a02c049f9b91e15f3b9a525a5\",\"dweb:/ipfs/QmbPQMVUVZBE9R9Va6FNfZBTrUY51nrjm8qxpeoAoRrPrQ\"]},\"lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol\":{\"keccak256\":\"0x6ead281d4569c26b3dee7313aefff24add906f3600b57d8aef1255a17d6b34e5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://aec0f9c9f14f829353663e1946bf0ea00a1771ff6ddb4f7cbe14893397b4b55c\",\"dweb:/ipfs/QmVMR3SdijdM2BpL9Yp3u7Riaxqgs1FnZ1Rfg2CMjKGtVS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol\":{\"keccak256\":\"0xf78f05f3b8c9f75570e85300d7b4600d7f6f6a198449273f31d44c1641adb46f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e28b872613b45e0e801d4995aa4380be2531147bfe2d85c1d6275f1de514fba3\",\"dweb:/ipfs/QmeeFcfShHYaS3BdgVj78nxR28ZaVUwbvr66ud8bT6kzw9\"]},\"lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e\",\"dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol\":{\"keccak256\":\"0xf46268c37522320bb2119a5a394bc5c739a95c0c574c8d08e8c643f4d06e5c76\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://517e4b295f35b9947c72ad7379a6089439ece7bb6f4a2ea0a159da13046c039e\",\"dweb:/ipfs/QmZXzkSfLUbvujig3zVbpDHykpHhqLpvQtdiN3B5j4TA3u\"]},\"lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol\":{\"keccak256\":\"0xc2dfdc8fbc8fdb142575c90568e237b809a1feaccff23e32d00131887a08dc36\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://dbc1d283ee77da43d61661b5ba1adeb427d6a6224335494a9a857dce41d9f1d6\",\"dweb:/ipfs/QmTnA35z7XzkxFSRUqB6CGVH1nhmvajwnDNfA5PiKmYuCi\"]},\"lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0xa4b9958797e0e9cde82a090525e90f80d5745ba1c67ee72b488bd3087498a17e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9344f7c2f80322c2e76d9d89bed03fd12f3e011e74fde7cf24ea21bdd2abe2d\",\"dweb:/ipfs/QmPMAjF5x2fHUAee2FKMZDBbFVrbZbPCr3a9KHLZaSn1zY\"]},\"lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0xad148d59f05165f9217d0a9e1ac8f772abb02ea6aaad8a756315c532bf79f9f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://15e3599867c2182f5831e9268b274b2ef2047825837df6b4d81c9e89254b093e\",\"dweb:/ipfs/QmZbL7XAYr5RmaNaooPgZRmcDXaudfsYQfYD9y5iAECvpS\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x2d9dc2fe26180f74c11c13663647d38e259e45f95eb88f57b61d2160b0109d3e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://81233d1f98060113d9922180bb0f14f8335856fe9f339134b09335e9f678c377\",\"dweb:/ipfs/QmWh6R35SarhAn4z2wH8SU456jJSYL2FgucfTFgbHJJN4E\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617\",\"dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"src/Scan2EarnNFT.sol\":{\"keccak256\":\"0x26dedd606c0b97107afcf6062a3865674c3984b59965cbdfb9d319260a2bb8b4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b52cd11a007bff660a29da47587d335f9437c4e43b6b2eac52a6a4e3b8107136\",\"dweb:/ipfs/QmbxVm2dGGDjCBehf9Do7rDqENtcpeAgqBxK87y51q6dnq\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "uint256", "name": "collectionId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "nftContract", "type": "address", "indexed": true}, {"internalType": "string", "name": "name", "type": "string", "indexed": false}, {"internalType": "enum Scan2EarnNFT.NFTType", "name": "nftType", "type": "uint8", "indexed": false}], "type": "event", "name": "CollectionCreated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "collectionId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenId", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "NFTMinted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "collections", "outputs": [{"internalType": "address", "name": "nftContract", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "expirationTime", "type": "uint256"}, {"internalType": "uint256", "name": "maxMintable", "type": "uint256"}, {"internalType": "uint256", "name": "currentMinted", "type": "uint256"}, {"internalType": "uint8", "name": "mediaUrlCount", "type": "uint8"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum Scan2EarnNFT.NFTType", "name": "nftType", "type": "uint8"}, {"internalType": "bool", "name": "isActive", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "contractToCollectionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "_nftContract", "type": "address"}, {"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "uint256", "name": "_expirationTime", "type": "uint256"}, {"internalType": "uint256", "name": "_maxMintable", "type": "uint256"}, {"internalType": "string[]", "name": "_mediaUrls", "type": "string[]"}, {"internalType": "string[]", "name": "_mediaTypes", "type": "string[]"}, {"internalType": "string", "name": "_title", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "enum Scan2EarnNFT.NFTType", "name": "_nftType", "type": "uint8"}, {"internalType": "string[]", "name": "_attributeNames", "type": "string[]"}, {"internalType": "string[]", "name": "_attributeValues", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "createCollection", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getCollection", "outputs": [{"internalType": "address", "name": "nftContract", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "expirationTime", "type": "uint256"}, {"internalType": "uint256", "name": "maxMintable", "type": "uint256"}, {"internalType": "uint256", "name": "currentMinted", "type": "uint256"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "enum Scan2EarnNFT.NFTType", "name": "nftType", "type": "uint8"}, {"internalType": "bool", "name": "isActive", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getCollectionAttributes", "outputs": [{"internalType": "struct Scan2EarnNFT.Attribute[]", "name": "", "type": "tuple[]", "components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "value", "type": "string"}]}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getCollectionMediaUrls", "outputs": [{"internalType": "struct Scan2EarnNFT.MediaUrl[]", "name": "", "type": "tuple[]", "components": [{"internalType": "string", "name": "url", "type": "string"}, {"internalType": "string", "name": "mediaType", "type": "string"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentCollectionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRemainingMintable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "isCollectionExpired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mintNFT"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "uint256", "name": "_collectionId", "type": "uint256"}, {"internalType": "bool", "name": "_isActive", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "updateCollectionStatus"}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/Scan2EarnNFT.sol": "Scan2EarnNFT"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x19fdfb0f3b89a230e7dbd1cf416f1a6b531a3ee5db4da483f946320fc74afc0e", "urls": ["bzz-raw://3490d794728f5bfecb46820431adaff71ba374141545ec20b650bb60353fac23", "dweb:/ipfs/QmPsfxjVpMcZbpE7BH93DzTpEaktESigEw4SmDzkXuJ4WR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol": {"keccak256": "0xb90916a13c108291699c6e6ddb4b1277ea860dfac118a60568a6f432baedcf68", "urls": ["bzz-raw://90719e236eafdcbdf56d830f460714668f95eb76c534edf140bb2948d9f8a048", "dweb:/ipfs/QmU8QmdKUHBejNeYGuJ913L7xhcysyr5MZLnNnPy9Jqrdx"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol": {"keccak256": "0xf189f9b417fe1931e1ab706838aff1128528694a9fcdb5ff7665197f2ca57d09", "urls": ["bzz-raw://2ff0143c836c8c9f85d13708733c09e21251395847fccfb518bf3b556726a840", "dweb:/ipfs/QmP69sjjrQrhYAsvCSSB69Bx66SiUPdQUqdzMYnf4wANHm"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol": {"keccak256": "0x6ec6d7fce29668ede560c7d2e10f9d10de3473f5298e431e70a5767db42fa620", "urls": ["bzz-raw://ac0139e51874aeec0730d040e57993187541777eb01d5939c06d5d2b986a54e8", "dweb:/ipfs/QmZbMbdPzusXuX9FGkyArV8hgzKLBZaL5RzMtCdCawtwPF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol": {"keccak256": "0x98c32de9b02f43eba7c0aba9fadf331cffb35a8d2076dce1d556c8f2cad704aa", "urls": ["bzz-raw://38f68f76e741cce3ca4e0f8ece5ab9d69a203829311a403bafc8f7b95a7e6d63", "dweb:/ipfs/QmZ8PrDXU5DiZ7fzEoRN7vHMQdemtsqvYVV5AdvAnScS4o"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol": {"keccak256": "0xaeca1511f7da49bdb16e44aa0f09dca76b51ba079bd068b2f80b8fe6d22b5fa1", "urls": ["bzz-raw://542fe084e72e14f4298954f70c407d5795c1207a02c049f9b91e15f3b9a525a5", "dweb:/ipfs/QmbPQMVUVZBE9R9Va6FNfZBTrUY51nrjm8qxpeoAoRrPrQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol": {"keccak256": "0x6ead281d4569c26b3dee7313aefff24add906f3600b57d8aef1255a17d6b34e5", "urls": ["bzz-raw://aec0f9c9f14f829353663e1946bf0ea00a1771ff6ddb4f7cbe14893397b4b55c", "dweb:/ipfs/QmVMR3SdijdM2BpL9Yp3u7Riaxqgs1FnZ1Rfg2CMjKGtVS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol": {"keccak256": "0xf78f05f3b8c9f75570e85300d7b4600d7f6f6a198449273f31d44c1641adb46f", "urls": ["bzz-raw://e28b872613b45e0e801d4995aa4380be2531147bfe2d85c1d6275f1de514fba3", "dweb:/ipfs/QmeeFcfShHYaS3BdgVj78nxR28ZaVUwbvr66ud8bT6kzw9"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0x88cd5e3bee2e8c36b8d9058fbcaa81ad5704281b25634122234b55ea853d8055", "urls": ["bzz-raw://8dc7e7ab5b8ea36c15027ab04221b05d1c970f47a53e9fd47ead8ca665d49c7e", "dweb:/ipfs/Qmeeph7fsDyfRr8vb2L8KcDEmKPb224TAayMvgqgGAnqpL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol": {"keccak256": "0xf46268c37522320bb2119a5a394bc5c739a95c0c574c8d08e8c643f4d06e5c76", "urls": ["bzz-raw://517e4b295f35b9947c72ad7379a6089439ece7bb6f4a2ea0a159da13046c039e", "dweb:/ipfs/QmZXzkSfLUbvujig3zVbpDHykpHhqLpvQtdiN3B5j4TA3u"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol": {"keccak256": "0xc2dfdc8fbc8fdb142575c90568e237b809a1feaccff23e32d00131887a08dc36", "urls": ["bzz-raw://dbc1d283ee77da43d61661b5ba1adeb427d6a6224335494a9a857dce41d9f1d6", "dweb:/ipfs/QmTnA35z7XzkxFSRUqB6CGVH1nhmvajwnDNfA5PiKmYuCi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0xa4b9958797e0e9cde82a090525e90f80d5745ba1c67ee72b488bd3087498a17e", "urls": ["bzz-raw://c9344f7c2f80322c2e76d9d89bed03fd12f3e011e74fde7cf24ea21bdd2abe2d", "dweb:/ipfs/QmPMAjF5x2fHUAee2FKMZDBbFVrbZbPCr3a9KHLZaSn1zY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3", "urls": ["bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a", "dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0xad148d59f05165f9217d0a9e1ac8f772abb02ea6aaad8a756315c532bf79f9f4", "urls": ["bzz-raw://15e3599867c2182f5831e9268b274b2ef2047825837df6b4d81c9e89254b093e", "dweb:/ipfs/QmZbL7XAYr5RmaNaooPgZRmcDXaudfsYQfYD9y5iAECvpS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x2d9dc2fe26180f74c11c13663647d38e259e45f95eb88f57b61d2160b0109d3e", "urls": ["bzz-raw://81233d1f98060113d9922180bb0f14f8335856fe9f339134b09335e9f678c377", "dweb:/ipfs/QmWh6R35SarhAn4z2wH8SU456jJSYL2FgucfTFgbHJJN4E"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x8891738ffe910f0cf2da09566928589bf5d63f4524dd734fd9cedbac3274dd5c", "urls": ["bzz-raw://971f954442df5c2ef5b5ebf1eb245d7105d9fbacc7386ee5c796df1d45b21617", "dweb:/ipfs/QmadRjHbkicwqwwh61raUEapaVEtaLMcYbQZWs9gUkgj3u"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "src/Scan2EarnNFT.sol": {"keccak256": "0x26dedd606c0b97107afcf6062a3865674c3984b59965cbdfb9d319260a2bb8b4", "urls": ["bzz-raw://b52cd11a007bff660a29da47587d335f9437c4e43b6b2eac52a6a4e3b8107136", "dweb:/ipfs/QmbxVm2dGGDjCBehf9Do7rDqENtcpeAgqBxK87y51q6dnq"], "license": "MIT"}}, "version": 1}, "id": 54}