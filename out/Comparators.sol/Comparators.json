{"abi": [], "bytecode": {"object": "0x6080806040523460175760399081601c823930815050f35b5f80fdfe5f80fdfea26469706673582212201e45b993ac7dbfaca12dd6a9653d8ab62dd414ba513573a5280b83b5f097f1aa64736f6c634300081a0033", "sourceMap": "224:218:38:-:0;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x5f80fdfea26469706673582212201e45b993ac7dbfaca12dd6a9653d8ab62dd414ba513573a5280b83b5f097f1aa64736f6c634300081a0033", "sourceMap": "224:218:38:-:0;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides a set of functions to compare values. _Available since v5.1._\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":\"Comparators\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts/contracts/utils/Comparators.sol": "Comparators"}, "evmVersion": "cancun", "libraries": {}, "viaIR": true}, "sources": {"lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}}, "version": 1}, "id": 38}