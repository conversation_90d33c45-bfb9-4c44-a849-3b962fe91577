// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract Scan2EarnERC721 is ERC721, ERC721URIStorage, Ownable {
    uint256 private _nextTokenId;

    // Mapping from token ID to custom metadata
    mapping(uint256 => string) private _tokenMetadata;

    constructor(
        string memory _name,
        string memory _symbol,
        address initialOwner
    ) ERC721(_name, _symbol) Ownable(initialOwner) {
        _nextTokenId = 1;
    }
    
    function safeMint(
        address to,
        string memory uri,
        string memory metadata
    ) public onlyOwner returns (uint256) {
        uint256 tokenId = _nextTokenId++;

        _safeMint(to, tokenId);
        _setTokenURI(tokenId, uri);
        _tokenMetadata[tokenId] = metadata;

        return tokenId;
    }
    
    function batchMint(
        address[] memory to,
        string[] memory uris,
        string[] memory metadatas
    ) public onlyOwner returns (uint256[] memory) {
        require(to.length == uris.length && uris.length == metadatas.length, "Arrays length mismatch");
        
        uint256[] memory tokenIds = new uint256[](to.length);
        
        for (uint256 i = 0; i < to.length; i++) {
            tokenIds[i] = safeMint(to[i], uris[i], metadatas[i]);
        }
        
        return tokenIds;
    }
    
    function getTokenMetadata(uint256 tokenId) public view returns (string memory) {
        _requireOwned(tokenId);
        return _tokenMetadata[tokenId];
    }

    function updateTokenMetadata(uint256 tokenId, string memory metadata) public onlyOwner {
        _requireOwned(tokenId);
        _tokenMetadata[tokenId] = metadata;
    }

    function getCurrentTokenId() public view returns (uint256) {
        return _nextTokenId;
    }
    
    // Burn function with metadata cleanup
    function burn(uint256 tokenId) public onlyOwner {
        delete _tokenMetadata[tokenId];
        _burn(tokenId);
    }

    function tokenURI(uint256 tokenId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
