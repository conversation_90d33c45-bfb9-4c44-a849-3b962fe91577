// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./Scan2EarnERC721.sol";
import "./Scan2EarnERC1155.sol";

contract Scan2EarnNFTPublic is Ownable, ReentrancyGuard {
    
    enum NFTType { ERC721, ERC1155 }
    
    struct MediaUrl {
        string url;
        string mediaType;
    }
    
    struct Attribute {
        string name;
        string value;
    }
    
    struct NFTCollection {
        address nftContract;
        address creator;
        string name;
        uint256 expirationTime;
        uint256 maxMintable;
        uint256 currentMinted;
        MediaUrl[10] mediaUrls;
        uint8 mediaUrlCount;
        string title;
        string description;
        NFTType nftType;
        Attribute[] attributes;
        bool isActive;
        bool publicMintEnabled;
    }
    
    uint256 private _nextCollectionId;
    mapping(uint256 => NFTCollection) public collections;
    mapping(address => uint256) public contractToCollectionId;
    mapping(address => uint256[]) public creatorCollections;
    
    event CollectionCreated(uint256 indexed collectionId, address indexed nftContract, address indexed creator);
    event NFTMinted(uint256 indexed collectionId, address indexed to, uint256 tokenId, uint256 amount);
    event PublicMintEnabled(uint256 indexed collectionId, bool enabled);
    
    constructor(address initialOwner) Ownable(initialOwner) {
        _nextCollectionId = 1;
    }
    
    function createCollection(
        string memory _contractName,
        string memory _contractSymbol,
        string memory _name,
        uint256 _expirationTime,
        uint256 _maxMintable,
        string[] memory _mediaUrls,
        string[] memory _mediaTypes,
        string memory _title,
        string memory _description,
        NFTType _nftType,
        string[] memory _attributeNames,
        string[] memory _attributeValues
    ) external returns (uint256) {
        require(_expirationTime > block.timestamp, "Invalid expiration");
        require(_maxMintable > 0, "Invalid max mintable");
        require(_mediaUrls.length <= 10, "Too many URLs");
        require(_mediaUrls.length == _mediaTypes.length, "URL/type mismatch");
        require(_attributeNames.length == _attributeValues.length, "Attribute mismatch");
        
        address nftContract;
        if (_nftType == NFTType.ERC721) {
            Scan2EarnERC721 erc721 = new Scan2EarnERC721(_contractName, _contractSymbol, address(this));
            nftContract = address(erc721);
        } else {
            Scan2EarnERC1155 erc1155 = new Scan2EarnERC1155("", address(this));
            nftContract = address(erc1155);
        }
        
        uint256 collectionId = _nextCollectionId++;
        
        NFTCollection storage collection = collections[collectionId];
        collection.nftContract = nftContract;
        collection.creator = msg.sender;
        collection.name = _name;
        collection.expirationTime = _expirationTime;
        collection.maxMintable = _maxMintable;
        collection.currentMinted = 0;
        collection.title = _title;
        collection.description = _description;
        collection.nftType = _nftType;
        collection.isActive = true;
        collection.publicMintEnabled = false;
        collection.mediaUrlCount = uint8(_mediaUrls.length);
        
        for (uint8 i = 0; i < _mediaUrls.length; i++) {
            collection.mediaUrls[i] = MediaUrl({
                url: _mediaUrls[i],
                mediaType: _mediaTypes[i]
            });
        }
        
        for (uint256 i = 0; i < _attributeNames.length; i++) {
            collection.attributes.push(Attribute({
                name: _attributeNames[i],
                value: _attributeValues[i]
            }));
        }
        
        contractToCollectionId[nftContract] = collectionId;
        creatorCollections[msg.sender].push(collectionId);
        
        emit CollectionCreated(collectionId, nftContract, msg.sender);
        
        return collectionId;
    }
    
    function setPublicMintEnabled(uint256 _collectionId, bool _enabled) external {
        NFTCollection storage collection = collections[_collectionId];
        require(collection.creator == msg.sender, "Only creator");
        
        collection.publicMintEnabled = _enabled;
        emit PublicMintEnabled(_collectionId, _enabled);
    }
    
    function mintNFT(
        uint256 _collectionId,
        address _to,
        uint256 _tokenId,
        uint256 _amount
    ) external nonReentrant {
        NFTCollection storage collection = collections[_collectionId];
        require(
            msg.sender == owner() || msg.sender == collection.creator,
            "Only owner or creator"
        );
        require(collection.isActive, "Not active");
        require(block.timestamp < collection.expirationTime, "Expired");
        require(collection.currentMinted + _amount <= collection.maxMintable, "Exceeds max");
        
        _performMint(collection, _to, _tokenId, _amount);
        
        emit NFTMinted(_collectionId, _to, _tokenId, _amount);
    }
    
    function publicMint(
        uint256 _collectionId,
        uint256 _tokenId,
        uint256 _amount
    ) external nonReentrant {
        NFTCollection storage collection = collections[_collectionId];
        
        require(collection.isActive, "Not active");
        require(collection.publicMintEnabled, "Public mint disabled");
        require(block.timestamp < collection.expirationTime, "Expired");
        require(collection.currentMinted + _amount <= collection.maxMintable, "Exceeds max");
        require(_amount > 0, "Invalid amount");
        
        _performMint(collection, msg.sender, _tokenId, _amount);
        
        emit NFTMinted(_collectionId, msg.sender, _tokenId, _amount);
    }
    
    function _performMint(
        NFTCollection storage collection,
        address to,
        uint256 tokenId,
        uint256 amount
    ) internal {
        if (collection.nftType == NFTType.ERC721) {
            require(amount == 1, "ERC721 can only mint 1");
            Scan2EarnERC721(collection.nftContract).safeMint(to, "", "");
        } else {
            Scan2EarnERC1155 erc1155Contract = Scan2EarnERC1155(collection.nftContract);
            if (!erc1155Contract.exists(tokenId)) {
                erc1155Contract.createNewToken(to, amount, "", "", amount, "");
            } else {
                erc1155Contract.mint(to, tokenId, amount, "", "", amount, "");
            }
        }
        
        collection.currentMinted += amount;
    }
    
    function getCollection(uint256 _collectionId) external view returns (
        address nftContract,
        address creator,
        string memory name,
        uint256 expirationTime,
        uint256 maxMintable,
        uint256 currentMinted,
        string memory title,
        string memory description,
        NFTType nftType,
        bool isActive,
        bool publicMintEnabled
    ) {
        NFTCollection storage collection = collections[_collectionId];
        return (
            collection.nftContract,
            collection.creator,
            collection.name,
            collection.expirationTime,
            collection.maxMintable,
            collection.currentMinted,
            collection.title,
            collection.description,
            collection.nftType,
            collection.isActive,
            collection.publicMintEnabled
        );
    }
    
    function getCollectionMediaUrls(uint256 _collectionId) external view returns (MediaUrl[] memory) {
        NFTCollection storage collection = collections[_collectionId];
        MediaUrl[] memory urls = new MediaUrl[](collection.mediaUrlCount);
        
        for (uint8 i = 0; i < collection.mediaUrlCount; i++) {
            urls[i] = collection.mediaUrls[i];
        }
        
        return urls;
    }
    
    function getCollectionAttributes(uint256 _collectionId) external view returns (Attribute[] memory) {
        return collections[_collectionId].attributes;
    }
    
    function getCreatorCollections(address _creator) external view returns (uint256[] memory) {
        return creatorCollections[_creator];
    }
    
    function getCurrentCollectionId() external view returns (uint256) {
        return _nextCollectionId;
    }
    
    function isCollectionExpired(uint256 _collectionId) external view returns (bool) {
        return block.timestamp >= collections[_collectionId].expirationTime;
    }
    
    function getRemainingMintable(uint256 _collectionId) external view returns (uint256) {
        NFTCollection storage collection = collections[_collectionId];
        return collection.maxMintable - collection.currentMinted;
    }
    
    function canPublicMint(uint256 _collectionId, uint256 _amount) external view returns (bool) {
        NFTCollection storage collection = collections[_collectionId];
        
        return collection.isActive &&
               collection.publicMintEnabled &&
               block.timestamp < collection.expirationTime &&
               collection.currentMinted + _amount <= collection.maxMintable &&
               _amount > 0;
    }
    
    function isPublicMintEnabled(uint256 _collectionId) external view returns (bool) {
        return collections[_collectionId].publicMintEnabled;
    }
}
