// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract Scan2EarnNFT is Ownable, ReentrancyGuard {
    using Counters for Counters.Counter;
    
    enum NFTType { ERC721, ERC1155 }
    
    struct MediaUrl {
        string url;
        string mediaType; // "image", "video", "audio", etc.
    }
    
    struct Attribute {
        string name;
        string value;
    }
    
    struct NFTCollection {
        address nftContract;
        string name;
        uint256 expirationTime;
        uint256 maxMintable;
        uint256 currentMinted;
        MediaUrl[10] mediaUrls; // Fixed array for up to 10 URLs
        uint8 mediaUrlCount; // Track actual number of URLs used
        string title;
        string description;
        NFTType nftType;
        Attribute[] attributes; // Dynamic array for name:value pairs
        bool isActive;
    }
    
    // Collection management
    Counters.Counter private _collectionIds;
    mapping(uint256 => NFTCollection) public collections;
    mapping(address => uint256) public contractToCollectionId;
    
    // Events
    event CollectionCreated(
        uint256 indexed collectionId,
        address indexed nftContract,
        string name,
        NFTType nftType
    );
    
    event NFTMinted(
        uint256 indexed collectionId,
        address indexed to,
        uint256 tokenId,
        uint256 amount
    );
    
    constructor() {}
    
    function createCollection(
        address _nftContract,
        string memory _name,
        uint256 _expirationTime,
        uint256 _maxMintable,
        string[] memory _mediaUrls,
        string[] memory _mediaTypes,
        string memory _title,
        string memory _description,
        NFTType _nftType,
        string[] memory _attributeNames,
        string[] memory _attributeValues
    ) external onlyOwner returns (uint256) {
        require(_nftContract != address(0), "Invalid contract address");
        require(_expirationTime > block.timestamp, "Expiration time must be in future");
        require(_maxMintable > 0, "Max mintable must be greater than 0");
        require(_mediaUrls.length <= 10, "Too many media URLs");
        require(_mediaUrls.length == _mediaTypes.length, "Media URLs and types length mismatch");
        require(_attributeNames.length == _attributeValues.length, "Attributes length mismatch");
        
        _collectionIds.increment();
        uint256 collectionId = _collectionIds.current();
        
        NFTCollection storage collection = collections[collectionId];
        collection.nftContract = _nftContract;
        collection.name = _name;
        collection.expirationTime = _expirationTime;
        collection.maxMintable = _maxMintable;
        collection.currentMinted = 0;
        collection.title = _title;
        collection.description = _description;
        collection.nftType = _nftType;
        collection.isActive = true;
        collection.mediaUrlCount = uint8(_mediaUrls.length);
        
        // Set media URLs
        for (uint8 i = 0; i < _mediaUrls.length; i++) {
            collection.mediaUrls[i] = MediaUrl({
                url: _mediaUrls[i],
                mediaType: _mediaTypes[i]
            });
        }
        
        // Set attributes
        for (uint256 i = 0; i < _attributeNames.length; i++) {
            collection.attributes.push(Attribute({
                name: _attributeNames[i],
                value: _attributeValues[i]
            }));
        }
        
        contractToCollectionId[_nftContract] = collectionId;
        
        emit CollectionCreated(collectionId, _nftContract, _name, _nftType);
        
        return collectionId;
    }
    
    function mintNFT(
        uint256 _collectionId,
        address _to,
        uint256 _tokenId,
        uint256 _amount
    ) external onlyOwner nonReentrant {
        NFTCollection storage collection = collections[_collectionId];
        
        require(collection.isActive, "Collection is not active");
        require(block.timestamp < collection.expirationTime, "Collection has expired");
        require(collection.currentMinted + _amount <= collection.maxMintable, "Exceeds max mintable");
        
        if (collection.nftType == NFTType.ERC721) {
            require(_amount == 1, "ERC721 can only mint 1 token at a time");
            ERC721(collection.nftContract).transferFrom(address(this), _to, _tokenId);
        } else {
            ERC1155(collection.nftContract).safeTransferFrom(address(this), _to, _tokenId, _amount, "");
        }
        
        collection.currentMinted += _amount;
        
        emit NFTMinted(_collectionId, _to, _tokenId, _amount);
    }
    
    function getCollection(uint256 _collectionId) external view returns (
        address nftContract,
        string memory name,
        uint256 expirationTime,
        uint256 maxMintable,
        uint256 currentMinted,
        string memory title,
        string memory description,
        NFTType nftType,
        bool isActive
    ) {
        NFTCollection storage collection = collections[_collectionId];
        return (
            collection.nftContract,
            collection.name,
            collection.expirationTime,
            collection.maxMintable,
            collection.currentMinted,
            collection.title,
            collection.description,
            collection.nftType,
            collection.isActive
        );
    }
    
    function getCollectionMediaUrls(uint256 _collectionId) external view returns (MediaUrl[] memory) {
        NFTCollection storage collection = collections[_collectionId];
        MediaUrl[] memory urls = new MediaUrl[](collection.mediaUrlCount);
        
        for (uint8 i = 0; i < collection.mediaUrlCount; i++) {
            urls[i] = collection.mediaUrls[i];
        }
        
        return urls;
    }
    
    function getCollectionAttributes(uint256 _collectionId) external view returns (Attribute[] memory) {
        return collections[_collectionId].attributes;
    }
    
    function updateCollectionStatus(uint256 _collectionId, bool _isActive) external onlyOwner {
        collections[_collectionId].isActive = _isActive;
    }
    
    function getCurrentCollectionId() external view returns (uint256) {
        return _collectionIds.current();
    }
    
    function isCollectionExpired(uint256 _collectionId) external view returns (bool) {
        return block.timestamp >= collections[_collectionId].expirationTime;
    }
    
    function getRemainingMintable(uint256 _collectionId) external view returns (uint256) {
        NFTCollection storage collection = collections[_collectionId];
        return collection.maxMintable - collection.currentMinted;
    }
}
