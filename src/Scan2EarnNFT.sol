// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./Scan2EarnERC721.sol";
import "./Scan2EarnERC1155.sol";

contract Scan2EarnNFT is Ownable, ReentrancyGuard {
    
    enum NFTType { ERC721, ERC1155 }
    
    struct MediaUrl {
        string url;
        string mediaType; // "image", "video", "audio", etc.
    }
    
    struct Attribute {
        string name;
        string value;
    }
    
    struct NFTCollection {
        address nftContract;
        address creator; // Người tạo collection
        string name;
        uint256 expirationTime;
        uint256 maxMintable;
        uint256 currentMinted;
        MediaUrl[10] mediaUrls; // Fixed array for up to 10 URLs
        uint8 mediaUrlCount; // Track actual number of URLs used
        string title;
        string description;
        NFTType nftType;
        Attribute[] attributes; // Dynamic array for name:value pairs
        bool isActive;
    }
    
    // Collection management
    uint256 private _nextCollectionId;
    mapping(uint256 => NFTCollection) public collections;
    mapping(address => uint256) public contractToCollectionId;
    mapping(address => uint256[]) public creatorCollections; // Track collections by creator
    
    // Events
    event CollectionCreated(
        uint256 indexed collectionId,
        address indexed nftContract,
        address indexed creator,
        string name,
        NFTType nftType
    );

    event CollectionUpdated(
        uint256 indexed collectionId,
        address indexed updater
    );

    event NFTMinted(
        uint256 indexed collectionId,
        address indexed to,
        uint256 tokenId,
        uint256 amount
    );
    
    constructor(address initialOwner) Ownable(initialOwner) {
        _nextCollectionId = 1;
    }
    
    function createCollection(
        string memory _contractName,
        string memory _contractSymbol,
        string memory _name,
        uint256 _expirationTime,
        uint256 _maxMintable,
        string[] memory _mediaUrls,
        string[] memory _mediaTypes,
        string memory _title,
        string memory _description,
        NFTType _nftType,
        string[] memory _attributeNames,
        string[] memory _attributeValues
    ) external returns (uint256) {
        require(_expirationTime > block.timestamp, "Expiration time must be in future");
        require(_maxMintable > 0, "Max mintable must be greater than 0");
        require(_mediaUrls.length <= 10, "Too many media URLs");
        require(_mediaUrls.length == _mediaTypes.length, "Media URLs and types length mismatch");
        require(_attributeNames.length == _attributeValues.length, "Attributes length mismatch");
        
        // Tự động tạo NFT contract với NFT manager làm owner để có thể mint
        address nftContract;
        if (_nftType == NFTType.ERC721) {
            Scan2EarnERC721 erc721 = new Scan2EarnERC721(_contractName, _contractSymbol, address(this));
            nftContract = address(erc721);
        } else {
            Scan2EarnERC1155 erc1155 = new Scan2EarnERC1155("", address(this));
            nftContract = address(erc1155);
        }

        uint256 collectionId = _nextCollectionId++;

        NFTCollection storage collection = collections[collectionId];
        collection.nftContract = nftContract;
        collection.creator = msg.sender;
        collection.name = _name;
        collection.expirationTime = _expirationTime;
        collection.maxMintable = _maxMintable;
        collection.currentMinted = 0;
        collection.title = _title;
        collection.description = _description;
        collection.nftType = _nftType;
        collection.isActive = true;
        collection.mediaUrlCount = uint8(_mediaUrls.length);
        
        // Set media URLs
        for (uint8 i = 0; i < _mediaUrls.length; i++) {
            collection.mediaUrls[i] = MediaUrl({
                url: _mediaUrls[i],
                mediaType: _mediaTypes[i]
            });
        }
        
        // Set attributes
        for (uint256 i = 0; i < _attributeNames.length; i++) {
            collection.attributes.push(Attribute({
                name: _attributeNames[i],
                value: _attributeValues[i]
            }));
        }
        
        contractToCollectionId[nftContract] = collectionId;
        creatorCollections[msg.sender].push(collectionId);

        emit CollectionCreated(collectionId, nftContract, msg.sender, _name, _nftType);
        
        return collectionId;
    }

    // Modifier để kiểm tra quyền edit
    modifier onlyCollectionCreator(uint256 _collectionId) {
        require(collections[_collectionId].creator == msg.sender, "Only collection creator can edit");
        require(collections[_collectionId].currentMinted == 0, "Cannot edit after minting started");
        _;
    }

    // Edit collection basic info
    function updateCollectionBasicInfo(
        uint256 _collectionId,
        string memory _name,
        string memory _title,
        string memory _description,
        uint256 _expirationTime,
        uint256 _maxMintable
    ) external onlyCollectionCreator(_collectionId) {
        require(_expirationTime > block.timestamp, "Expiration time must be in future");
        require(_maxMintable > 0, "Max mintable must be greater than 0");

        NFTCollection storage collection = collections[_collectionId];
        collection.name = _name;
        collection.title = _title;
        collection.description = _description;
        collection.expirationTime = _expirationTime;
        collection.maxMintable = _maxMintable;

        emit CollectionUpdated(_collectionId, msg.sender);
    }

    // Edit media URLs
    function updateCollectionMediaUrls(
        uint256 _collectionId,
        string[] memory _mediaUrls,
        string[] memory _mediaTypes
    ) external onlyCollectionCreator(_collectionId) {
        require(_mediaUrls.length <= 10, "Too many media URLs");
        require(_mediaUrls.length == _mediaTypes.length, "Media URLs and types length mismatch");

        NFTCollection storage collection = collections[_collectionId];

        // Clear existing URLs
        for (uint8 i = 0; i < collection.mediaUrlCount; i++) {
            delete collection.mediaUrls[i];
        }

        // Set new URLs
        collection.mediaUrlCount = uint8(_mediaUrls.length);
        for (uint8 i = 0; i < _mediaUrls.length; i++) {
            collection.mediaUrls[i] = MediaUrl({
                url: _mediaUrls[i],
                mediaType: _mediaTypes[i]
            });
        }

        emit CollectionUpdated(_collectionId, msg.sender);
    }

    // Edit attributes
    function updateCollectionAttributes(
        uint256 _collectionId,
        string[] memory _attributeNames,
        string[] memory _attributeValues
    ) external onlyCollectionCreator(_collectionId) {
        require(_attributeNames.length == _attributeValues.length, "Attributes length mismatch");

        NFTCollection storage collection = collections[_collectionId];

        // Clear existing attributes
        delete collection.attributes;

        // Set new attributes
        for (uint256 i = 0; i < _attributeNames.length; i++) {
            collection.attributes.push(Attribute({
                name: _attributeNames[i],
                value: _attributeValues[i]
            }));
        }

        emit CollectionUpdated(_collectionId, msg.sender);
    }
    
    function mintNFT(
        uint256 _collectionId,
        address _to,
        uint256 _tokenId,
        uint256 _amount
    ) external nonReentrant {
        NFTCollection storage collection = collections[_collectionId];
        require(
            msg.sender == owner() || msg.sender == collection.creator,
            "Only owner or collection creator can mint"
        );
        require(collection.isActive, "Collection is not active");
        require(block.timestamp < collection.expirationTime, "Collection has expired");
        require(collection.currentMinted + _amount <= collection.maxMintable, "Exceeds max mintable");
        
        if (collection.nftType == NFTType.ERC721) {
            require(_amount == 1, "ERC721 can only mint 1 token at a time");
            Scan2EarnERC721(collection.nftContract).safeMint(_to, "", "");
        } else {
            // For ERC1155, we need to create new token if it doesn't exist
            Scan2EarnERC1155 erc1155Contract = Scan2EarnERC1155(collection.nftContract);
            if (!erc1155Contract.exists(_tokenId)) {
                erc1155Contract.createNewToken(_to, _amount, "", "", _amount, "");
            } else {
                erc1155Contract.mint(_to, _tokenId, _amount, "", "", _amount, "");
            }
        }
        
        collection.currentMinted += _amount;
        
        emit NFTMinted(_collectionId, _to, _tokenId, _amount);
    }
    
    function getCollection(uint256 _collectionId) external view returns (
        address nftContract,
        address creator,
        string memory name,
        uint256 expirationTime,
        uint256 maxMintable,
        uint256 currentMinted,
        string memory title,
        string memory description,
        NFTType nftType,
        bool isActive
    ) {
        NFTCollection storage collection = collections[_collectionId];
        return (
            collection.nftContract,
            collection.creator,
            collection.name,
            collection.expirationTime,
            collection.maxMintable,
            collection.currentMinted,
            collection.title,
            collection.description,
            collection.nftType,
            collection.isActive
        );
    }
    
    function getCollectionMediaUrls(uint256 _collectionId) external view returns (MediaUrl[] memory) {
        NFTCollection storage collection = collections[_collectionId];
        MediaUrl[] memory urls = new MediaUrl[](collection.mediaUrlCount);
        
        for (uint8 i = 0; i < collection.mediaUrlCount; i++) {
            urls[i] = collection.mediaUrls[i];
        }
        
        return urls;
    }
    
    function getCollectionAttributes(uint256 _collectionId) external view returns (Attribute[] memory) {
        return collections[_collectionId].attributes;
    }
    
    function updateCollectionStatus(uint256 _collectionId, bool _isActive) external onlyOwner {
        collections[_collectionId].isActive = _isActive;
    }
    
    function getCurrentCollectionId() external view returns (uint256) {
        return _nextCollectionId;
    }
    
    function isCollectionExpired(uint256 _collectionId) external view returns (bool) {
        return block.timestamp >= collections[_collectionId].expirationTime;
    }
    
    function getRemainingMintable(uint256 _collectionId) external view returns (uint256) {
        NFTCollection storage collection = collections[_collectionId];
        return collection.maxMintable - collection.currentMinted;
    }

    // Get collections created by a specific creator
    function getCreatorCollections(address _creator) external view returns (uint256[] memory) {
        return creatorCollections[_creator];
    }

    // Check if user can edit collection
    function canEditCollection(uint256 _collectionId, address _user) external view returns (bool) {
        NFTCollection storage collection = collections[_collectionId];
        return collection.creator == _user && collection.currentMinted == 0;
    }

    // Get collection creator
    function getCollectionCreator(uint256 _collectionId) external view returns (address) {
        return collections[_collectionId].creator;
    }
}
