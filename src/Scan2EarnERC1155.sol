// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Supply.sol";

contract Scan2EarnERC1155 is ERC1155, ERC1155Supply, Ownable {
    uint256 private _nextTokenId;
    
    // Mapping from token ID to URI
    mapping(uint256 => string) private _tokenURIs;
    
    // Mapping from token ID to custom metadata
    mapping(uint256 => string) private _tokenMetadata;
    
    // Mapping from token ID to max supply
    mapping(uint256 => uint256) private _maxSupply;
    
    constructor(
        string memory baseURI,
        address initialOwner
    ) ERC1155(baseURI) Ownable(initialOwner) {
        _nextTokenId = 1;
    }
    
    function mint(
        address to,
        uint256 id,
        uint256 amount,
        string memory tokenURI,
        string memory metadata,
        uint256 maxSupply,
        bytes memory data
    ) public onlyOwner {
        require(id > 0, "Token ID must be greater than 0");
        
        if (_maxSupply[id] == 0) {
            // First time minting this token ID
            _maxSupply[id] = maxSupply;
            _tokenURIs[id] = tokenURI;
            _tokenMetadata[id] = metadata;
        }
        
        require(totalSupply(id) + amount <= _maxSupply[id], "Exceeds maximum supply");
        
        _mint(to, id, amount, data);
    }
    
    function mintBatch(
        address to,
        uint256[] memory ids,
        uint256[] memory amounts,
        string[] memory tokenURIs,
        string[] memory metadatas,
        uint256[] memory maxSupplies,
        bytes memory data
    ) public onlyOwner {
        require(
            ids.length == amounts.length && 
            amounts.length == tokenURIs.length && 
            tokenURIs.length == metadatas.length &&
            metadatas.length == maxSupplies.length,
            "Arrays length mismatch"
        );
        
        for (uint256 i = 0; i < ids.length; i++) {
            require(ids[i] > 0, "Token ID must be greater than 0");
            
            if (_maxSupply[ids[i]] == 0) {
                // First time minting this token ID
                _maxSupply[ids[i]] = maxSupplies[i];
                _tokenURIs[ids[i]] = tokenURIs[i];
                _tokenMetadata[ids[i]] = metadatas[i];
            }
            
            require(totalSupply(ids[i]) + amounts[i] <= _maxSupply[ids[i]], "Exceeds maximum supply");
        }
        
        _mintBatch(to, ids, amounts, data);
    }
    
    function createNewToken(
        address to,
        uint256 amount,
        string memory tokenURI,
        string memory metadata,
        uint256 maxSupply,
        bytes memory data
    ) public onlyOwner returns (uint256) {
        uint256 tokenId = _nextTokenId++;
        
        _maxSupply[tokenId] = maxSupply;
        _tokenURIs[tokenId] = tokenURI;
        _tokenMetadata[tokenId] = metadata;
        
        _mint(to, tokenId, amount, data);
        
        return tokenId;
    }
    
    function uri(uint256 tokenId) public view override returns (string memory) {
        string memory tokenURI = _tokenURIs[tokenId];
        return bytes(tokenURI).length > 0 ? tokenURI : super.uri(tokenId);
    }
    
    function getTokenMetadata(uint256 tokenId) public view returns (string memory) {
        require(exists(tokenId), "Token does not exist");
        return _tokenMetadata[tokenId];
    }
    
    function updateTokenMetadata(uint256 tokenId, string memory metadata) public onlyOwner {
        require(exists(tokenId), "Token does not exist");
        _tokenMetadata[tokenId] = metadata;
    }
    
    function updateTokenURI(uint256 tokenId, string memory newURI) public onlyOwner {
        require(exists(tokenId), "Token does not exist");
        _tokenURIs[tokenId] = newURI;
    }
    
    function getMaxSupply(uint256 tokenId) public view returns (uint256) {
        return _maxSupply[tokenId];
    }
    
    function getRemainingSupply(uint256 tokenId) public view returns (uint256) {
        require(exists(tokenId), "Token does not exist");
        return _maxSupply[tokenId] - totalSupply(tokenId);
    }
    
    function getCurrentTokenId() public view returns (uint256) {
        return _nextTokenId;
    }
    
    function exists(uint256 tokenId) public view override returns (bool) {
        return _maxSupply[tokenId] > 0;
    }
    
    // Override functions
    function _update(
        address from,
        address to,
        uint256[] memory ids,
        uint256[] memory values
    ) internal override(ERC1155, ERC1155Supply) {
        super._update(from, to, ids, values);
    }
    
    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC1155)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
