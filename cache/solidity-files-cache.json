{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "src", "tests": "test", "scripts": "script", "libraries": ["lib"]}, "files": {"lib/forge-std/src/Base.sol": {"lastModificationDate": 1753368152409, "contentHash": "b30affbf365427e2", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Base.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.26": {"default": {"path": "Base.sol/CommonBase.json", "build_id": "394abb675a5d5de1"}}}, "ScriptBase": {"0.8.26": {"default": {"path": "Base.sol/ScriptBase.json", "build_id": "394abb675a5d5de1"}}}, "TestBase": {"0.8.26": {"default": {"path": "Base.sol/TestBase.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Script.sol": {"lastModificationDate": 1753368152409, "contentHash": "654eb74437773a2d", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Script.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Script": {"0.8.26": {"default": {"path": "Script.sol/Script.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": 1753368152409, "contentHash": "e29aa8aa08237766", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdAssertions.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.26": {"default": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": 1753368152410, "contentHash": "a40952ce0d242817", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdChains.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.26": {"default": {"path": "StdChains.sol/StdChains.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": 1753368152410, "contentHash": "30325e8cda32c7ae", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdCheats.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.26": {"default": {"path": "StdCheats.sol/StdCheats.json", "build_id": "394abb675a5d5de1"}}}, "StdCheatsSafe": {"0.8.26": {"default": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdConstants.sol": {"lastModificationDate": 1753368152410, "contentHash": "23303eb7e922efe4", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdConstants.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdConstants": {"0.8.26": {"default": {"path": "StdConstants.sol/StdConstants.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": 1753368152410, "contentHash": "a1a86c7115e2cdf3", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdError.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.26": {"default": {"path": "StdError.sol/stdError.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": 1753368152410, "contentHash": "0111ef959dff6f54", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdInvariant.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.26": {"default": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": 1753368152410, "contentHash": "5fb1b35c8fb281fd", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdJson.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.26": {"default": {"path": "StdJson.sol/stdJson.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": 1753368152411, "contentHash": "72584abebada1e7a", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdMath.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.26": {"default": {"path": "StdMath.sol/stdMath.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": 1753368152411, "contentHash": "c05daa9a55282c5b", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStorage.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.26": {"default": {"path": "StdStorage.sol/stdStorage.json", "build_id": "394abb675a5d5de1"}}}, "stdStorageSafe": {"0.8.26": {"default": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": 1753368152411, "contentHash": "ee166ef95092736e", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStyle.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.26": {"default": {"path": "StdStyle.sol/StdStyle.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdToml.sol": {"lastModificationDate": 1753368152411, "contentHash": "fc667e4ecb7fa86c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdToml.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdToml": {"0.8.26": {"default": {"path": "StdToml.sol/stdToml.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": 1753368152411, "contentHash": "804c508a1dad250e", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdUtils.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.26": {"default": {"path": "StdUtils.sol/StdUtils.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": 1753368152412, "contentHash": "f56119a09f81c62c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Test.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.26": {"default": {"path": "Test.sol/Test.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1753368152443, "contentHash": "c751e602355186f4", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Vm.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.26": {"default": {"path": "Vm.sol/Vm.json", "build_id": "394abb675a5d5de1"}}}, "VmSafe": {"0.8.26": {"default": {"path": "Vm.sol/VmSafe.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1753368152412, "contentHash": "bae85493a76fb054", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console.sol", "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.26": {"default": {"path": "console.sol/console.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1753368152412, "contentHash": "49a7da3dfc404603", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console2.sol", "imports": ["lib/forge-std/src/console.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1753368152413, "contentHash": "b680a332ebf10901", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.26": {"default": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/forge-std/src/safeconsole.sol": {"lastModificationDate": 1753368152413, "contentHash": "621653b34a6691ea", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/safeconsole.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"safeconsole": {"0.8.26": {"default": {"path": "safeconsole.sol/safeconsole.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"lastModificationDate": 1753369738705, "contentHash": "aeede215495e3727", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Context.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Ownable": {"0.8.26": {"default": {"path": "Ownable.sol/Ownable.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"lastModificationDate": 1753369738713, "contentHash": "1a826f6d4b769022", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.4.16", "artifacts": {}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol": {"lastModificationDate": 1753369738714, "contentHash": "6e8681389f973d3b", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC4906": {"0.8.26": {"default": {"path": "IERC4906.sol/IERC4906.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol": {"lastModificationDate": 1753369738714, "contentHash": "3907f2e494f53470", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "imports": ["lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1753369738824, "contentHash": "9c740010cc7bb5db", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "imports": [], "versionRequirement": ">=0.8.4", "artifacts": {"IERC1155Errors": {"0.8.26": {"default": {"path": "draft-IERC6093.sol/IERC1155Errors.json", "build_id": "394abb675a5d5de1"}}}, "IERC20Errors": {"0.8.26": {"default": {"path": "draft-IERC6093.sol/IERC20Errors.json", "build_id": "394abb675a5d5de1"}}}, "IERC721Errors": {"0.8.26": {"default": {"path": "draft-IERC6093.sol/IERC721Errors.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol": {"lastModificationDate": 1753369738726, "contentHash": "e7283ad7fa223c58", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC1155": {"0.8.26": {"default": {"path": "ERC1155.sol/ERC1155.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol": {"lastModificationDate": 1753369738726, "contentHash": "c6c9467e17ad15a7", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC1155": {"0.8.26": {"default": {"path": "IERC1155.sol/IERC1155.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol": {"lastModificationDate": 1753369738726, "contentHash": "4fc300270f4702e8", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC1155Receiver": {"0.8.26": {"default": {"path": "IERC1155Receiver.sol/IERC1155Receiver.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol": {"lastModificationDate": 1753369738726, "contentHash": "7ea0901ee67d4d2f", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC1155Supply": {"0.8.26": {"default": {"path": "ERC1155Supply.sol/ERC1155Supply.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol": {"lastModificationDate": 1753369738726, "contentHash": "a8e51f2ad39dcfc0", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "imports": ["lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC1155MetadataURI": {"0.8.26": {"default": {"path": "IERC1155MetadataURI.sol/IERC1155MetadataURI.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol": {"lastModificationDate": 1753369738726, "contentHash": "783dbb551e3f072c", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC1155Utils": {"0.8.26": {"default": {"path": "ERC1155Utils.sol/ERC1155Utils.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol": {"lastModificationDate": 1753369738827, "contentHash": "a13181b91d40fdb9", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC721": {"0.8.26": {"default": {"path": "ERC721.sol/ERC721.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol": {"lastModificationDate": 1753369738728, "contentHash": "4e12b50dd570d4a2", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC721": {"0.8.26": {"default": {"path": "IERC721.sol/IERC721.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"lastModificationDate": 1753369738729, "contentHash": "ab027c534171d9e1", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "imports": [], "versionRequirement": ">=0.5.0", "artifacts": {"IERC721Receiver": {"0.8.26": {"default": {"path": "IERC721Receiver.sol/IERC721Receiver.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol": {"lastModificationDate": 1753369738827, "contentHash": "c055874acdf179d2", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC721URIStorage": {"0.8.26": {"default": {"path": "ERC721URIStorage.sol/ERC721URIStorage.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol": {"lastModificationDate": 1753369738730, "contentHash": "93e08c85430105d6", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "imports": ["lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": ">=0.6.2", "artifacts": {"IERC721Metadata": {"0.8.26": {"default": {"path": "IERC721Metadata.sol/IERC721Metadata.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol": {"lastModificationDate": 1753369738730, "contentHash": "74ed4ff1cdc7bb1c", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "imports": ["lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC721Utils": {"0.8.26": {"default": {"path": "ERC721Utils.sol/ERC721Utils.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"lastModificationDate": 1753369738730, "contentHash": "7aeadaaa15f7bc13", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Arrays": {"0.8.26": {"default": {"path": "Arrays.sol/Arrays.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"lastModificationDate": 1753369738731, "contentHash": "6341a97e9912da46", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Comparators": {"0.8.26": {"default": {"path": "Comparators.sol/Comparators.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"lastModificationDate": 1753369738731, "contentHash": "16db1f8b2f7183f5", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Context": {"0.8.26": {"default": {"path": "Context.sol/Context.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"lastModificationDate": 1753369738732, "contentHash": "cfb5098ef78673ff", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"Panic": {"0.8.26": {"default": {"path": "Panic.sol/Panic.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1753369738732, "contentHash": "6c9ca3bfbd20d2c1", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"ReentrancyGuard": {"0.8.26": {"default": {"path": "ReentrancyGuard.sol/ReentrancyGuard.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"lastModificationDate": 1753369738732, "contentHash": "21271603e761ca55", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"SlotDerivation": {"0.8.26": {"default": {"path": "SlotDerivation.sol/SlotDerivation.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1753369738732, "contentHash": "261e9fcb6515866e", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"StorageSlot": {"0.8.26": {"default": {"path": "StorageSlot.sol/StorageSlot.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"lastModificationDate": 1753369738828, "contentHash": "a698169a4d9ee0a2", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Strings": {"0.8.26": {"default": {"path": "Strings.sol/Strings.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"lastModificationDate": 1753369738734, "contentHash": "25715299aa3db066", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"], "versionRequirement": "^0.8.20", "artifacts": {"ERC165": {"0.8.26": {"default": {"path": "ERC165.sol/ERC165.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1753369738735, "contentHash": "021ac46c8076d0ee", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "imports": [], "versionRequirement": ">=0.4.16", "artifacts": {"IERC165": {"0.8.26": {"default": {"path": "IERC165.sol/IERC165.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"lastModificationDate": 1753369738735, "contentHash": "f578cd1eb517fca5", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"Math": {"0.8.26": {"default": {"path": "Math.sol/Math.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1753369738735, "contentHash": "5a907d9c96fd0da2", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "imports": [], "versionRequirement": "^0.8.20", "artifacts": {"SafeCast": {"0.8.26": {"default": {"path": "SafeCast.sol/SafeCast.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1753369738735, "contentHash": "d7e482c0d6f136d7", "interfaceReprHash": null, "sourceName": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "imports": ["lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.20", "artifacts": {"SignedMath": {"0.8.26": {"default": {"path": "SignedMath.sol/SignedMath.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "script/Deploy.s.sol": {"lastModificationDate": 1753372719190, "contentHash": "9c1dc7bd172e0eca", "interfaceReprHash": null, "sourceName": "script/Deploy.s.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/NFTFactory.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol", "src/Scan2EarnNFTPublic.sol"], "versionRequirement": "^0.8.26", "artifacts": {"DeployScript": {"0.8.26": {"default": {"path": "Deploy.s.sol/DeployScript.json", "build_id": "2e226784b2af1cf4"}}}}, "seenByCompiler": true}, "script/DeploySimple.s.sol": {"lastModificationDate": 1753371326491, "contentHash": "0b131697cc70d9ed", "interfaceReprHash": null, "sourceName": "script/DeploySimple.s.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/Script.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/NFTFactory.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol", "src/Scan2EarnNFT.sol"], "versionRequirement": "^0.8.26", "artifacts": {"DeploySimpleScript": {"0.8.26": {"default": {"path": "DeploySimple.s.sol/DeploySimpleScript.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "src/NFTFactory.sol": {"lastModificationDate": 1753369483972, "contentHash": "7f06265aab150ed5", "interfaceReprHash": null, "sourceName": "src/NFTFactory.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol"], "versionRequirement": "^0.8.26", "artifacts": {"NFTFactory": {"0.8.26": {"default": {"path": "NFTFactory.sol/NFTFactory.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "src/Scan2EarnERC1155.sol": {"lastModificationDate": 1753370662397, "contentHash": "b4247c5eda92a79a", "interfaceReprHash": null, "sourceName": "src/Scan2EarnERC1155.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnERC1155": {"0.8.26": {"default": {"path": "Scan2EarnERC1155.sol/Scan2EarnERC1155.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "src/Scan2EarnERC721.sol": {"lastModificationDate": 1753369865075, "contentHash": "e012b720afa0ae21", "interfaceReprHash": null, "sourceName": "src/Scan2EarnERC721.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnERC721": {"0.8.26": {"default": {"path": "Scan2EarnERC721.sol/Scan2EarnERC721.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "src/Scan2EarnNFT.sol": {"lastModificationDate": 1753371659289, "contentHash": "b82be78c63457e97", "interfaceReprHash": null, "sourceName": "src/Scan2EarnNFT.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnNFT": {"0.8.26": {"default": {"path": "Scan2EarnNFT.sol/Scan2EarnNFT.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}, "src/Scan2EarnNFTPublic.sol": {"lastModificationDate": 1753372672081, "contentHash": "e54abbed5d9e87c1", "interfaceReprHash": null, "sourceName": "src/Scan2EarnNFTPublic.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnNFTPublic": {"0.8.26": {"default": {"path": "Scan2EarnNFTPublic.sol/Scan2EarnNFTPublic.json", "build_id": "8d0127911fbe65c6"}}}}, "seenByCompiler": true}, "src/Scan2EarnNFTSimple.sol": {"lastModificationDate": 1753372597643, "contentHash": "c907556e4df3fae5", "interfaceReprHash": null, "sourceName": "src/Scan2EarnNFTSimple.sol", "imports": ["lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnNFTSimple": {"0.8.26": {"default": {"path": "Scan2EarnNFTSimple.sol/Scan2EarnNFTSimple.json", "build_id": "134d24a989e647ce"}}}}, "seenByCompiler": true}, "test/Scan2EarnNFT.t.sol": {"lastModificationDate": 1753370641010, "contentHash": "5dfd34ec008279bc", "interfaceReprHash": null, "sourceName": "test/Scan2EarnNFT.t.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC4906.sol", "lib/openzeppelin-contracts/contracts/interfaces/IERC721.sol", "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/ERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/ERC1155Supply.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/extensions/IERC1155MetadataURI.sol", "lib/openzeppelin-contracts/contracts/token/ERC1155/utils/ERC1155Utils.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/ERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/ERC721URIStorage.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/extensions/IERC721Metadata.sol", "lib/openzeppelin-contracts/contracts/token/ERC721/utils/ERC721Utils.sol", "lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "lib/openzeppelin-contracts/contracts/utils/Context.sol", "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "src/NFTFactory.sol", "src/Scan2EarnERC1155.sol", "src/Scan2EarnERC721.sol", "src/Scan2EarnNFT.sol"], "versionRequirement": "^0.8.26", "artifacts": {"Scan2EarnNFTTest": {"0.8.26": {"default": {"path": "Scan2EarnNFT.t.sol/Scan2EarnNFTTest.json", "build_id": "394abb675a5d5de1"}}}}, "seenByCompiler": true}}, "builds": ["134d24a989e647ce", "2e226784b2af1cf4", "394abb675a5d5de1", "8d0127911fbe65c6"], "profiles": {"default": {"solc": {"optimizer": {"enabled": true, "runs": 1000}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode.object", "evm.bytecode.sourceMap", "evm.bytecode.linkReferences", "evm.deployedBytecode.object", "evm.deployedBytecode.sourceMap", "evm.deployedBytecode.linkReferences", "evm.deployedBytecode.immutableReferences", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "cancun", "viaIR": true, "libraries": {}}, "vyper": {"evmVersion": "cancun", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}}, "preprocessed": false, "mocks": []}