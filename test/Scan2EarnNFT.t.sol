// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Test.sol";
import "../src/Scan2EarnNFT.sol";
import "../src/Scan2EarnERC721.sol";
import "../src/Scan2EarnERC1155.sol";
import "../src/NFTFactory.sol";

contract Scan2EarnNFTTest is Test {
    Scan2EarnNFT public nftManager;
    NFTFactory public factory;
    Scan2EarnERC721 public erc721Contract;
    Scan2EarnERC1155 public erc1155Contract;
    
    address public owner = address(0x1);
    address public user1 = address(0x2);
    address public user2 = address(0x3);
    
    function setUp() public {
        vm.startPrank(owner);
        
        // Deploy contracts
        nftManager = new Scan2EarnNFT(owner);
        factory = new NFTFactory(owner);
        
        // Create ERC721 and ERC1155 contracts
        address erc721Address = factory.createERC721("Test NFT 721", "T721");
        address erc1155Address = factory.createERC1155("https://api.example.com/metadata/{id}.json");
        
        erc721Contract = Scan2EarnERC721(erc721Address);
        erc1155Contract = Scan2EarnERC1155(erc1155Address);
        
        vm.stopPrank();
    }
    
    function testCreateERC721Collection() public {
        vm.startPrank(user1);

        string[] memory mediaUrls = new string[](2);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";
        mediaUrls[1] = "https://ipfs.io/ipfs/QmHash2";

        string[] memory mediaTypes = new string[](2);
        mediaTypes[0] = "image";
        mediaTypes[1] = "video";

        string[] memory attributeNames = new string[](3);
        attributeNames[0] = "rarity";
        attributeNames[1] = "power";
        attributeNames[2] = "element";

        string[] memory attributeValues = new string[](3);
        attributeValues[0] = "legendary";
        attributeValues[1] = "100";
        attributeValues[2] = "fire";

        uint256 collectionId = nftManager.createCollection(
            "Test NFT",
            "TNFT",
            "Test Collection",
            block.timestamp + 30 days,
            1000,
            mediaUrls,
            mediaTypes,
            "Amazing NFT Collection",
            "This is a test collection for ERC721 tokens",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );

        assertEq(collectionId, 1);

        // Test collection data
        (
            address nftContract,
            address creator,
            string memory name,
            uint256 expirationTime,
            uint256 maxMintable,
            uint256 currentMinted,
            string memory title,
            string memory description,
            Scan2EarnNFT.NFTType nftType,
            bool isActive
        ) = nftManager.getCollection(collectionId);

        assertTrue(nftContract != address(0));
        assertEq(creator, user1);
        assertEq(name, "Test Collection");
        assertEq(maxMintable, 1000);
        assertEq(currentMinted, 0);
        assertEq(title, "Amazing NFT Collection");
        assertTrue(isActive);
        assertEq(uint(nftType), uint(Scan2EarnNFT.NFTType.ERC721));

        vm.stopPrank();
    }
    
    function testCreateERC1155Collection() public {
        vm.startPrank(user1);

        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash3";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](2);
        attributeNames[0] = "type";
        attributeNames[1] = "level";

        string[] memory attributeValues = new string[](2);
        attributeValues[0] = "weapon";
        attributeValues[1] = "5";

        uint256 collectionId = nftManager.createCollection(
            "Test ERC1155",
            "T1155",
            "Test ERC1155 Collection",
            block.timestamp + 60 days,
            5000,
            mediaUrls,
            mediaTypes,
            "Multi-Token Collection",
            "This is a test collection for ERC1155 tokens",
            Scan2EarnNFT.NFTType.ERC1155,
            attributeNames,
            attributeValues
        );

        assertEq(collectionId, 1);

        vm.stopPrank();
    }
    
    function testGetCollectionMediaUrls() public {
        vm.startPrank(user1);

        string[] memory mediaUrls = new string[](3);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";
        mediaUrls[1] = "https://ipfs.io/ipfs/QmHash2";
        mediaUrls[2] = "https://ipfs.io/ipfs/QmHash3";

        string[] memory mediaTypes = new string[](3);
        mediaTypes[0] = "image";
        mediaTypes[1] = "video";
        mediaTypes[2] = "audio";

        string[] memory attributeNames = new string[](1);
        attributeNames[0] = "test";

        string[] memory attributeValues = new string[](1);
        attributeValues[0] = "value";

        uint256 collectionId = nftManager.createCollection(
            "Media Test NFT",
            "MTNFT",
            "Media Test Collection",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Media Test",
            "Testing media URLs",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );
        
        Scan2EarnNFT.MediaUrl[] memory retrievedUrls = nftManager.getCollectionMediaUrls(collectionId);
        
        assertEq(retrievedUrls.length, 3);
        assertEq(retrievedUrls[0].url, "https://ipfs.io/ipfs/QmHash1");
        assertEq(retrievedUrls[0].mediaType, "image");
        assertEq(retrievedUrls[1].url, "https://ipfs.io/ipfs/QmHash2");
        assertEq(retrievedUrls[1].mediaType, "video");
        assertEq(retrievedUrls[2].url, "https://ipfs.io/ipfs/QmHash3");
        assertEq(retrievedUrls[2].mediaType, "audio");
        
        vm.stopPrank();
    }
    
    function testGetCollectionAttributes() public {
        vm.startPrank(user1);

        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](4);
        attributeNames[0] = "rarity";
        attributeNames[1] = "power";
        attributeNames[2] = "element";
        attributeNames[3] = "generation";

        string[] memory attributeValues = new string[](4);
        attributeValues[0] = "epic";
        attributeValues[1] = "85";
        attributeValues[2] = "water";
        attributeValues[3] = "1";

        uint256 collectionId = nftManager.createCollection(
            "Attribute Test NFT",
            "ATNFT",
            "Attribute Test Collection",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Attribute Test",
            "Testing attributes",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );
        
        Scan2EarnNFT.Attribute[] memory attributes = nftManager.getCollectionAttributes(collectionId);
        
        assertEq(attributes.length, 4);
        assertEq(attributes[0].name, "rarity");
        assertEq(attributes[0].value, "epic");
        assertEq(attributes[1].name, "power");
        assertEq(attributes[1].value, "85");
        assertEq(attributes[2].name, "element");
        assertEq(attributes[2].value, "water");
        assertEq(attributes[3].name, "generation");
        assertEq(attributes[3].value, "1");
        
        vm.stopPrank();
    }
    
    function testFactoryDeployment() public {
        vm.startPrank(owner);
        
        // Test ERC721 creation
        address erc721Address = factory.createERC721("Factory Test 721", "FT721");
        assertTrue(erc721Address != address(0));
        
        // Test ERC1155 creation
        address erc1155Address = factory.createERC1155("https://factory.test/{id}.json");
        assertTrue(erc1155Address != address(0));
        
        // Check factory tracking
        assertEq(factory.getTotalERC721Contracts(), 2); // Including setUp contract
        assertEq(factory.getTotalERC1155Contracts(), 2); // Including setUp contract
        
        address[] memory ownerERC721s = factory.getCreatorERC721Contracts(owner);
        address[] memory ownerERC1155s = factory.getCreatorERC1155Contracts(owner);
        
        assertEq(ownerERC721s.length, 2);
        assertEq(ownerERC1155s.length, 2);
        
        vm.stopPrank();
    }

    function testEditCollection() public {
        vm.startPrank(user1);

        // Create collection first
        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](1);
        attributeNames[0] = "test";

        string[] memory attributeValues = new string[](1);
        attributeValues[0] = "value";

        uint256 collectionId = nftManager.createCollection(
            "Edit Test NFT",
            "ETNFT",
            "Original Name",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Original Title",
            "Original Description",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );

        // Test editing basic info
        nftManager.updateCollectionBasicInfo(
            collectionId,
            "Updated Name",
            "Updated Title",
            "Updated Description",
            block.timestamp + 60 days,
            200
        );

        // Verify changes
        (,, string memory name,,,, string memory title, string memory description,,) = nftManager.getCollection(collectionId);
        assertEq(name, "Updated Name");
        assertEq(title, "Updated Title");
        assertEq(description, "Updated Description");

        // Test editing media URLs
        string[] memory newMediaUrls = new string[](2);
        newMediaUrls[0] = "https://ipfs.io/ipfs/QmNewHash1";
        newMediaUrls[1] = "https://ipfs.io/ipfs/QmNewHash2";

        string[] memory newMediaTypes = new string[](2);
        newMediaTypes[0] = "image";
        newMediaTypes[1] = "video";

        nftManager.updateCollectionMediaUrls(collectionId, newMediaUrls, newMediaTypes);

        // Verify media URLs
        Scan2EarnNFT.MediaUrl[] memory urls = nftManager.getCollectionMediaUrls(collectionId);
        assertEq(urls.length, 2);
        assertEq(urls[0].url, "https://ipfs.io/ipfs/QmNewHash1");
        assertEq(urls[1].url, "https://ipfs.io/ipfs/QmNewHash2");

        // Test editing attributes
        string[] memory newAttributeNames = new string[](2);
        newAttributeNames[0] = "rarity";
        newAttributeNames[1] = "power";

        string[] memory newAttributeValues = new string[](2);
        newAttributeValues[0] = "epic";
        newAttributeValues[1] = "90";

        nftManager.updateCollectionAttributes(collectionId, newAttributeNames, newAttributeValues);

        // Verify attributes
        Scan2EarnNFT.Attribute[] memory attributes = nftManager.getCollectionAttributes(collectionId);
        assertEq(attributes.length, 2);
        assertEq(attributes[0].name, "rarity");
        assertEq(attributes[0].value, "epic");
        assertEq(attributes[1].name, "power");
        assertEq(attributes[1].value, "90");

        vm.stopPrank();
    }

    function testCannotEditAfterMinting() public {
        vm.startPrank(user1);

        // Create collection
        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](1);
        attributeNames[0] = "test";

        string[] memory attributeValues = new string[](1);
        attributeValues[0] = "value";

        uint256 collectionId = nftManager.createCollection(
            "Mint Test NFT",
            "MTNFT",
            "Test Collection",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Test Title",
            "Test Description",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );

        // Mint one NFT
        nftManager.mintNFT(collectionId, user2, 1, 1);

        // Try to edit - should fail
        vm.expectRevert("Cannot edit after minting started");
        nftManager.updateCollectionBasicInfo(
            collectionId,
            "Should Fail",
            "Should Fail",
            "Should Fail",
            block.timestamp + 60 days,
            200
        );

        vm.stopPrank();
    }

    function testOnlyCreatorCanEdit() public {
        vm.startPrank(user1);

        // Create collection
        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](1);
        attributeNames[0] = "test";

        string[] memory attributeValues = new string[](1);
        attributeValues[0] = "value";

        uint256 collectionId = nftManager.createCollection(
            "Creator Test NFT",
            "CTNFT",
            "Test Collection",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Test Title",
            "Test Description",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );

        vm.stopPrank();

        // Try to edit as different user - should fail
        vm.startPrank(user2);
        vm.expectRevert("Only collection creator can edit");
        nftManager.updateCollectionBasicInfo(
            collectionId,
            "Should Fail",
            "Should Fail",
            "Should Fail",
            block.timestamp + 60 days,
            200
        );

        vm.stopPrank();
    }

    function testGetCreatorCollections() public {
        vm.startPrank(user1);

        // Create multiple collections
        string[] memory mediaUrls = new string[](1);
        mediaUrls[0] = "https://ipfs.io/ipfs/QmHash1";

        string[] memory mediaTypes = new string[](1);
        mediaTypes[0] = "image";

        string[] memory attributeNames = new string[](1);
        attributeNames[0] = "test";

        string[] memory attributeValues = new string[](1);
        attributeValues[0] = "value";

        uint256 collectionId1 = nftManager.createCollection(
            "Collection 1",
            "C1",
            "Collection 1",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Title 1",
            "Description 1",
            Scan2EarnNFT.NFTType.ERC721,
            attributeNames,
            attributeValues
        );

        uint256 collectionId2 = nftManager.createCollection(
            "Collection 2",
            "C2",
            "Collection 2",
            block.timestamp + 30 days,
            100,
            mediaUrls,
            mediaTypes,
            "Title 2",
            "Description 2",
            Scan2EarnNFT.NFTType.ERC1155,
            attributeNames,
            attributeValues
        );

        // Get creator collections
        uint256[] memory userCollections = nftManager.getCreatorCollections(user1);
        assertEq(userCollections.length, 2);
        assertEq(userCollections[0], collectionId1);
        assertEq(userCollections[1], collectionId2);

        vm.stopPrank();
    }
}
